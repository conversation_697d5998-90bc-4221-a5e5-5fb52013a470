# OMS系统集成的领星ERP API接口汇总表

## 🛒 订单管理相关API

| API接口 | 请求路径 | 功能描述 | 限流 | 代码路径 | 文档链接 |
|---------|----------|----------|------|----------|----------|
| **FBM订单列表** | `/erp/sc/routing/order/Order/getOrderList` | 查询亚马逊自发货订单列表 | 1次/秒 | `oms-third-api-center/src/main/java/com/dhp/oms/third/api/module/order/FbmOrderApi.java` | [文档](https://apidoc.lingxing.com/#/docs/Sale/FBMOrderList) |
| **FBM订单详情** | `/erp/sc/routing/order/Order/getOrderDetail` | 查询亚马逊自发货订单详情 | 1次/秒 | `oms-third-api-center/src/main/java/com/dhp/oms/third/api/module/order/FbmOrderApi.java` | [文档](https://apidoc.lingxing.com/#/docs/Sale/FBMOrderDetail) |
| **MWS订单列表** | `/erp/sc/data/mws/orders` | 查询亚马逊平台订单列表 | 1次/秒 | `oms-third-api-center/src/main/java/com/dhp/oms/third/api/module/order/MwsOrderApi.java` | [文档](https://apidoc.lingxing.com/#/docs/Sale/Orderlists) |
| **MWS订单详情** | `/erp/sc/data/mws/orderDetail` | 查询亚马逊平台订单详情 | 1次/秒 | `oms-third-api-center/src/main/java/com/dhp/oms/third/api/module/order/MwsOrderApi.java` | [文档](https://apidoc.lingxing.com/#/docs/Sale/OrderDetail) |
| **多平台订单列表** | `/pb/mp/order/v2/list` | 查询多平台订单管理列表 | 10次/秒 | `oms-third-api-center/src/main/java/com/dhp/oms/third/api/module/order/MpOrderApi.java` | [文档](https://apidoc.lingxing.com/#/docs/MultiPlatform/V2/MultiPlatOrderV2) |

## 📦 商品管理相关API

| API接口 | 请求路径 | 功能描述 | 限流 | 代码路径 | 文档链接 |
|---------|----------|----------|------|----------|----------|
| **SKU产品列表** | `/erp/sc/routing/data/local_inventory/productList` | 查询本地产品(SKU)列表 | 20次/秒 | `oms-third-api-center/src/main/java/com/dhp/oms/third/api/module/sku/ProductSkuApi.java` | [文档](https://apidoc.lingxing.com/#/docs/Product/ProductLists) |
| **SKU产品详情** | `/erp/sc/routing/data/local_inventory/batchGetProductInfo` | 批量获取产品详情 | 20次/秒 | `oms-third-api-center/src/main/java/com/dhp/oms/third/api/module/sku/ProductSkuApi.java` | [文档](https://apidoc.lingxing.com/#/docs/Product/batchGetProductInfo) |
| **SKU产品编辑** | `/erp/sc/routing/storage/product/set` | 新增/编辑SKU产品 | 10次/秒 | `oms-third-api-center/src/main/java/com/dhp/oms/third/api/module/sku/ProductSkuApi.java` | [文档](https://apidoc.lingxing.com/#/docs/Product/SetProduct) |
| **SPU列表查询** | `/erp/sc/routing/storage/spu/spuList` | 获取SPU列表 | 10次/秒 | `oms-third-api-center/src/main/java/com/dhp/oms/third/api/module/spu/ProductSpuApi.java` | [文档](https://apidoc.lingxing.com/#/docs/Product/spuList) |
| **SPU详情查询** | `/erp/sc/routing/storage/spu/info` | 获取SPU详情 | 10次/秒 | `oms-third-api-center/src/main/java/com/dhp/oms/third/api/module/spu/ProductSpuApi.java` | [文档](https://apidoc.lingxing.com/#/docs/Product/spuInfo) |
| **SPU编辑** | `/erp/sc/routing/storage/spu/set` | 新增/编辑SPU | 5次/秒 | `oms-third-api-center/src/main/java/com/dhp/oms/third/api/module/spu/ProductSpuApi.java` | [文档](https://apidoc.lingxing.com/#/docs/Product/spuSet) |
| **商品分类列表** | `/erp/sc/routing/data/local_inventory/category` | 查询商品分类列表 | 无限制 | `oms-third-api-center/src/main/java/com/dhp/oms/third/api/module/product/category/impl/ProductCategoryApiImpl.java` | - |
| **商品分类编辑** | `/erp/sc/routing/storage/category/set` | 新增/编辑商品分类 | 无限制 | `oms-third-api-center/src/main/java/com/dhp/oms/third/api/module/product/category/impl/ProductCategoryApiImpl.java` | - |

## 🏪 店铺管理相关API

| API接口 | 请求路径 | 功能描述 | 限流 | 代码路径 | 文档链接 |
|---------|----------|----------|------|----------|----------|
| **亚马逊店铺列表** | `/erp/sc/data/seller/lists` | 查询已授权的亚马逊店铺信息 | 无限制 | `oms-third-api-center/src/main/java/com/dhp/oms/third/api/module/shop/impl/ShopApiImpl.java` | - |
| **多平台店铺列表** | `/pb/mp/shop/v2/getSellerList` | 查询多平台店铺信息 | 无限制 | `oms-third-api-center/src/main/java/com/dhp/oms/third/api/module/shop/impl/ShopApiImpl.java` | - |

## 🛍️ 商品刊登相关API

| API接口 | 请求路径 | 功能描述 | 限流 | 代码路径 | 文档链接 |
|---------|----------|----------|------|----------|----------|
| **亚马逊Listing列表** | `/erp/sc/data/mws/listing` | 查询亚马逊商品刊登列表 | 无限制 | `oms-third-api-center/src/main/java/com/dhp/oms/third/api/module/listing/impl/StoreListingApiImpl.java` | - |
| **多平台Listing列表** | `/pb/mp/listing/v2/getPairList` | 查询多平台商品刊登列表 | 无限制 | `oms-third-api-center/src/main/java/com/dhp/oms/third/api/module/listing/impl/StoreListingApiImpl.java` | - |

## 🛒 采购管理相关API

| API接口 | 请求路径 | 功能描述 | 限流 | 代码路径 | 文档链接 |
|---------|----------|----------|------|----------|----------|
| **采购计划列表** | `/erp/sc/routing/data/local_inventory/getPurchasePlans` | 查询采购计划列表 | 无限制 | `oms-third-api-center/src/main/java/com/dhp/oms/third/api/module/purchase/PurchaseApi.java` | [文档](https://apidoc.lingxing.com/#/docs/Purchase/getPurchasePlans) |
| **创建采购单** | `/erp/sc/routing/purchase/purchase/createPurchaseOrder` | 创建待到货的采购单 | 无限制 | `oms-third-api-center/src/main/java/com/dhp/oms/third/api/module/purchase/PurchaseApi.java` | [文档](https://apidoc.lingxing.com/#/docs/Purchase/CreatePurchaseOrder) |
| **添加入库单** | `/erp/sc/routing/storage/storage/orderAdd` | 添加入库单 | 无限制 | `oms-third-api-center/src/main/java/com/dhp/oms/third/api/module/purchase/PurchaseApi.java` | [文档](https://apidoc.lingxing.com/#/docs/Warehouse/OrderAdd) |
| **作废采购计划** | `/basicOpen/purchase/planCancel` | 作废采购计划 | 无限制 | `oms-third-api-center/src/main/java/com/dhp/oms/third/api/module/purchase/PurchaseApi.java` | [文档](https://apidoc.lingxing.com/#/docs/Purchase/PurchasePlanCancel) |

## 📦 出库管理相关API

| API接口 | 请求路径 | 功能描述 | 限流 | 代码路径 | 文档链接 |
|---------|----------|----------|------|----------|----------|
| **销售出库单列表** | `/erp/sc/routing/wms/order/wmsOrderList` | 查询销售出库单列表 | 无限制 | `oms-third-api-center/src/main/java/com/dhp/oms/third/api/module/outboundorder/OutboundOrderApi.java` | [文档](https://apidoc.lingxing.com/#/docs/Warehouse/WmsOrderList) |
| **亚马逊发货报表** | `/erp/sc/data/mws_report_v1/getAmazonFulfilledShipmentsList` | 查询亚马逊源报表发货信息 | 无限制 | `oms-third-api-center/src/main/java/com/dhp/oms/third/api/module/outboundorder/OutboundOrderApi.java` | [文档](https://apidoc.lingxing.com/#/docs/SourceData/v1getAmazonFulfilledShipmentsList) |

## 🔧 售后服务相关API

| API接口 | 请求路径 | 功能描述 | 限流 | 代码路径 | 文档链接 |
|---------|----------|----------|------|----------|----------|
| **售后工单列表** | `/pb/mp/returns/workOrder/list` | 查询售后工单列表 | 无限制 | `oms-third-api-center/src/main/java/com/dhp/oms/third/api/module/afs/AfsApi.java` | - |
| **FBA退货订单** | `/erp/sc/data/mws_report/refundOrders` | 查询亚马逊FBA退货订单 | 1次/秒 | `oms-third-api-center/src/main/java/com/dhp/oms/third/api/module/afs/AfsApi.java` | [文档](https://apidoc.lingxing.com/#/docs/SourceData/RefundOrders) |
| **FBA换货订单** | `/erp/sc/routing/data/order/fbaExchangeOrderList` | 查询亚马逊FBA换货订单 | 1次/秒 | `oms-third-api-center/src/main/java/com/dhp/oms/third/api/module/afs/AfsApi.java` | [文档](https://apidoc.lingxing.com/#/docs/SourceData/fbaExchangeOrderList) |
| **售后订单列表** | `/erp/sc/routing/amzod/order/afterSaleList` | 查询售后订单列表 | 1次/秒 | `oms-third-api-center/src/main/java/com/dhp/oms/third/api/module/afs/AfsApi.java` | [文档](https://apidoc.lingxing.com/#/docs/Sale/afterSaleList) |

## 👥 供应商管理相关API

| API接口 | 请求路径 | 功能描述 | 限流 | 代码路径 | 文档链接 |
|---------|----------|----------|------|----------|----------|
| **供应商列表** | `/erp/sc/data/local_inventory/supplier` | 查询供应商列表 | 无限制 | `oms-third-api-center/src/main/java/com/dhp/oms/third/api/module/supplier/impl/SupplierApiImpl.java` | - |

## 📧 邮件服务相关API

| API接口 | 请求路径 | 功能描述 | 限流 | 代码路径 | 文档链接 |
|---------|----------|----------|------|----------|----------|
| **接收邮件列表** | 待实现 | 获取接收邮件列表 | 无限制 | `oms-third-api-center/src/main/java/com/dhp/oms/third/api/module/email/EmailServiceApi.java` | - |
| **发送邮件列表** | 待实现 | 获取发送邮件列表 | 无限制 | `oms-third-api-center/src/main/java/com/dhp/oms/third/api/module/email/EmailServiceApi.java` | - |
| **邮件详情** | 待实现 | 获取邮件详情 | 无限制 | `oms-third-api-center/src/main/java/com/dhp/oms/third/api/module/email/EmailServiceApi.java` | - |

## 🔧 核心配置和工具类

| 组件类型 | 功能描述 | 代码路径 |
|----------|----------|----------|
| **API配置类** | 领星API基础配置 | `oms-third-api-center/src/main/java/com/dhp/oms/third/api/properties/LingxingApiProperties.java` |
| **统一API客户端** | 统一的HTTP调用封装 | `oms-third-api-center/src/main/java/com/dhp/oms/third/api/common/ExternalApiClient.java` |
| **Feign拦截器** | API调用拦截和签名处理 | `oms-third-api-center/src/main/java/com/dhp/oms/third/api/module/base/configuration/LingxingFeignInterceptor.java` |
| **Token服务** | 访问令牌管理 | `oms-third-api-center/src/main/java/com/dhp/oms/third/api/module/token/service/TokenService.java` |
| **自动配置类** | Spring Boot自动配置 | `oms-third-api-center/src/main/java/com/dhp/oms/third/api/ThirdApiAutoConfiguration.java` |
| **平台枚举** | 支持的电商平台定义 | `oms-third-api-center/src/main/java/com/dhp/oms/third/api/extern/enums/MpPlatformCodeEnum.java` |
