# 测试开发规范

## 测试结构

### 单元测试
- 测试类命名：`{被测试类名}Test`
- 放置在test目录下，包结构与源码保持一致
- 使用JUnit 5 + Mockito框架

### 集成测试  
- 测试类命名：`{模块名}IntegrationTest`
- 使用@SpringBootTest注解
- 测试完整的业务流程

## 测试方法命名

使用描述性命名，格式：`should_{期望结果}_when_{输入条件}`

示例：
```java
@Test
void should_return_order_details_when_order_id_exists() {
    // 测试逻辑
}

@Test  
void should_throw_exception_when_order_not_found() {
    // 测试逻辑
}
```

## Mock使用规范

- 使用@Mock注解创建Mock对象
- 使用@InjectMocks注入被测试对象
- 使用when().thenReturn()设置Mock行为
- 使用verify()验证方法调用

## 测试数据准备

- 使用@Transactional + @Rollback确保测试数据隔离
- 使用TestDataBuilder模式构建测试数据
- 避免硬编码测试数据，使用常量或配置文件

## 断言规范

- 优先使用AssertJ提供的流式断言
- 断言信息要清晰，便于定位问题
- 每个测试方法只验证一个行为

## 测试覆盖率

- 核心业务逻辑覆盖率不低于80%
- 工具类和公共方法覆盖率不低于90%
- 异常情况必须有相应测试用例

## 性能测试

- 关键接口需要性能测试
- 使用@Test(timeout = xxx)设置超时
- 大数据量处理需要压力测试
 