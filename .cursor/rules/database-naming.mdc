# 数据库开发规范

## 表命名规范

- 表名使用小写字母，单词间用下划线分隔
- 业务表以业务模块为前缀，如：`order_`, `product_`, `inventory_`
- 关联表命名：`{主表}_{关联表}`，如：`order_product`

## 字段命名规范

- 字段名使用小写字母，单词间用下划线分隔
- 主键统一命名为`id`
- 外键命名：`{关联表名}_id`，如：`order_id`
- 布尔字段使用`is_`前缀，如：`is_deleted`
- 时间字段使用明确含义，如：`created_time`, `updated_time`

## MyBatis Mapper规范

### 实体类映射
- 实体类使用@TableName指定表名
- 字段使用@TableField处理特殊映射
- 主键使用@TableId指定策略

### Mapper接口
- 继承BaseMapper<T>获得基础CRUD方法
- 自定义方法命名清晰，体现业务含义
- 复杂查询方法使用@Select注解或XML实现

### XML配置
- 放置在resources/mapper目录下
- 文件名与Mapper接口保持一致
- ResultMap命名：`{实体类名}ResultMap`
- SQL片段使用`<sql>`标签复用

## 查询性能优化

- 避免使用`SELECT *`，明确指定需要的字段
- 合理使用索引，避免全表扫描
- 大数据量查询必须分页
- 批量操作使用批处理方式

## 事务管理

- 服务层方法使用@Transactional注解
- 只读操作使用readOnly=true
- 合理设置事务传播级别和隔离级别
 