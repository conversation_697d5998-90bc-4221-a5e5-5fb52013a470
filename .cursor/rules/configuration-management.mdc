# 配置管理规范

## 配置文件结构

### application.yaml结构
按以下顺序组织配置：
1. server配置（端口、context-path等）
2. spring核心配置（应用名、profiles等）
3. 数据源配置
4. 中间件配置（Redis、MQ等）
5. 自定义业务配置
6. 日志配置

### 环境配置分离
- `application.yaml`: 通用配置
- `application-dev.yaml`: 开发环境
- `application-test.yaml`: 测试环境  
- `application-prod.yaml`: 生产环境

## 配置类规范

### 自定义配置属性
```java
@Data
@Component
@ConfigurationProperties(prefix = "oms.business")
public class BusinessProperties {
    private String defaultStatus = "ACTIVE";
    private Integer pageSize = 20;
}
```

### 配置注入
- 使用@Value注入简单配置
- 使用@ConfigurationProperties注入复杂配置对象
- 配置类使用@Validated进行验证

## 敏感信息处理

- 数据库密码等敏感信息使用环境变量
- 生产环境配置文件不提交到版本控制
- 使用Spring Cloud Config或其他配置中心

## 配置验证

- 关键配置项必须有默认值
- 使用@NotNull、@Min等注解验证配置
- 启动时验证必要配置项

## 多环境配置

### 激活profiles
```yaml
spring:
  profiles:
    active: dev
```

### 条件配置
```java
@Configuration
@Profile("!prod")
public class DevConfiguration {
    // 开发环境专用配置
}
```

## 配置刷新

- 使用@RefreshScope支持配置热刷新
- 重要配置变更需要重启应用
- 提供配置变更通知机制
 