# Java编码规范

## 基本规范

1. **使用Java 17语法特性**
   - 优先使用var关键字（适当场景）
   - 使用Text Blocks处理多行字符串
   - 使用Pattern Matching（适当场景）

2. **Spring Boot规范**
   - 使用@RestController替代@Controller + @ResponseBody
   - 使用@Autowired进行依赖注入，推荐构造器注入
   - 配置类使用@Configuration + @Bean

3. **MyBatis-Plus规范**
   - 实体类继承BaseEntity（如果存在）
   - 使用@TableName指定表名
   - 使用@TableId指定主键策略
   - Mapper继承BaseMapper<T>

## 命名约定

- **类名**: 大驼峰命名法（PascalCase）
- **方法名/变量名**: 小驼峰命名法（camelCase）
- **常量**: 全大写，下划线分隔（UPPER_SNAKE_CASE）
- **包名**: 全小写，点分隔

## 注解使用

- 实体类必须添加合适的验证注解（@NotNull, @Valid等）
- API方法必须添加@ApiOperation或相关文档注解
- 服务层方法建议添加@Transactional（需要事务时）

## 异常处理

- 使用项目定义的BusinessException处理业务异常
- 使用统一的异常处理器处理全局异常
- 错误信息使用国际化支持

## 日志规范

- 使用slf4j记录日志
- 关键业务操作必须记录日志
- 敏感信息不得记录到日志中
 