# API开发规范

## REST API设计原则

1. **HTTP方法使用**
   - GET: 查询操作
   - POST: 创建操作
   - PUT: 完整更新操作
   - PATCH: 部分更新操作
   - DELETE: 删除操作

2. **URL命名**
   - 使用名词，避免动词
   - 使用复数形式：`/api/users` 而不是 `/api/user`
   - 层级关系清晰：`/api/orders/{orderId}/items`

3. **响应格式**
   - 统一使用ResponseData包装返回结果
   - 成功响应包含data字段
   - 错误响应包含错误码和错误信息

## 请求/响应DTO规范

### 请求DTO (Request)
- 放置在`web.request`包下
- 类名以Request结尾，如：`CreateOrderRequest`
- 必须添加数据验证注解
- 使用@ApiModel和@ApiModelProperty添加文档

### 响应DTO (Response)  
- 放置在`web.response`包下
- 类名以Response结尾，如：`OrderDetailResponse`
- 使用@ApiModel和@ApiModelProperty添加文档

## 控制器规范

### 基本结构
```java
@RestController
@RequestMapping("/api/v1/orders")
@Api(tags = "订单管理")
public class OrderController {
    
    @Autowired
    private OrderService orderService;
    
    @PostMapping
    @ApiOperation("创建订单")
    public ResponseData<OrderResponse> createOrder(@Valid @RequestBody CreateOrderRequest request) {
        // 实现逻辑
    }
}
```

### 注解使用
- 类级别使用@Api(tags = "模块名称")
- 方法级别使用@ApiOperation("操作描述")
- 参数验证使用@Valid
- 路径参数使用@PathVariable
- 查询参数使用@RequestParam

## 分页查询规范

- 查询请求继承分页基类（如PageRequest）
- 响应使用Page<T>包装分页结果
- 默认页大小不超过100条记录
 