# Cursor 规则说明

本目录包含针对OMS（订单管理系统）项目的Cursor编程规则，旨在统一开发规范，提高代码质量和开发效率。

## 规则文件列表

### 1. project-structure.mdc
**应用范围**: 所有请求（alwaysApply: true）
**作用**: 提供项目整体结构指南，包括模块划分、包命名规范、关键文件位置等。

### 2. java-coding-standards.mdc  
**应用范围**: 所有 *.java 文件
**作用**: Java编码规范，包括命名约定、注解使用、异常处理、日志规范等。

### 3. api-development.mdc
**应用范围**: API相关文件（**/web/api/*.java, **/web/request/*.java, **/web/response/*.java）
**作用**: REST API开发规范，包括HTTP方法使用、URL命名、DTO规范、控制器规范等。

### 4. database-naming.mdc
**应用范围**: 数据库相关文件（**/mapper/*.java, **/mapper/*.xml, **/*Mapper.java, **/*Mapper.xml）
**作用**: 数据库开发规范，包括表命名、字段命名、MyBatis配置、性能优化等。

### 5. testing-standards.mdc
**应用范围**: 测试文件（**/test/**/*.java, **/*Test.java, **/*Tests.java）
**作用**: 测试开发规范，包括测试结构、命名规范、Mock使用、断言规范等。

### 6. configuration-management.mdc
**应用范围**: 配置文件（**/application*.yaml, **/application*.yml, **/application*.properties, **/config/*.java）
**作用**: 配置管理规范，包括配置文件结构、环境分离、敏感信息处理等。

## 使用方式

这些规则会在相应的文件类型中自动生效，帮助AI助手：
- 理解项目结构和架构
- 遵循统一的编码规范
- 提供符合项目要求的代码建议
- 快速定位关键文件和组件

## 维护说明

- 规则文件使用Markdown格式，扩展名为.mdc
- 每个规则文件开头包含frontmatter元数据
- 可根据项目发展需要更新和扩展规则内容
- 新增规则时请更新本README文档 