# OMS项目结构指南

这是一个基于Spring Boot 3.3.13和Java 17的多模块OMS（订单管理系统）项目。

## 模块结构

- **oms-framework**: 公共框架模块，提供基础组件和配置
  - 入口配置: [OmsAutoConfiguration.java](mdc:oms-framework/src/main/java/com/dhp/oms/framework/OmsAutoConfiguration.java)
  - 基础实体: [oms-framework/src/main/java/com/dhp/oms/framework/core/entity](mdc:oms-framework/src/main/java/com/dhp/oms/framework/core/entity)
  - 工具类: [oms-framework/src/main/java/com/dhp/oms/framework/utils](mdc:oms-framework/src/main/java/com/dhp/oms/framework/utils)

- **oms-ops-service**: 运营服务模块，核心业务逻辑
  - 主启动类: [OpsApplication.java](mdc:oms-ops-service/src/main/java/com/dhp/oms/ops/OpsApplication.java)
  - 配置文件: [application.yaml](mdc:oms-ops-service/src/main/resources/application.yaml)
  - API控制器: [oms-ops-service/src/main/java/com/dhp/oms/ops/web/api](mdc:oms-ops-service/src/main/java/com/dhp/oms/ops/web/api)

- **oms-fact-service**: 事实服务模块
  - 主启动类: [FactApplication.java](mdc:oms-fact-service/src/main/java/com/dhp/oms/fact/FactApplication.java)

- **oms-data-schedule**: 数据调度模块，处理定时任务
  - 主启动类: [DsApplication.java](mdc:oms-data-schedule/src/main/java/com/dhp/oms/ds/DsApplication.java)
  - 任务处理器: [oms-data-schedule/src/main/java/com/dhp/oms/ds/jobhandler](mdc:oms-data-schedule/src/main/java/com/dhp/oms/ds/jobhandler)

- **oms-third-api-center**: 第三方API集成中心
  - 自动配置: [ThirdApiAutoConfiguration.java](mdc:oms-third-api-center/src/main/java/com/dhp/oms/third/api/ThirdApiAutoConfiguration.java)

## 包命名规范

- 基础包名: `com.dhp.oms`
- 模块包名: `com.dhp.oms.{module}`
- 分层包名:
  - `web.api`: REST API控制器
  - `web.request`: 请求DTO
  - `web.response`: 响应DTO
  - `infrastructure`: 基础设施层
  - `config`: 配置类
  - `common`: 公共组件

## 数据库配置

项目使用MySQL数据库，配置在各模块的application.yaml文件中。
 