# OMS系统XXL-Job定时任务汇总

## ⚙️ XXL-Job配置信息

### 基础配置
| 配置项 | 配置值 | 说明 |
|--------|--------|------|
| **调度中心地址** | `http://114.215.252.56:8501/xxl-job-admin` | XXL-Job管理后台 |
| **执行器AppName** | `xxl-job-executor-sample` | 执行器应用名称 |
| **执行器端口** | `9999` | 执行器通信端口 |
| **访问令牌** | `default_token` | 安全访问令牌 |
| **日志路径** | `/data/applogs/xxl-job/jobhandler` | 任务日志存储路径 |

### 代码路径
- **配置类**: `oms-data-schedule/src/main/java/com/dhp/oms/ds/config/XxlJobConfig.java`
- **任务处理器**: `oms-data-schedule/src/main/java/com/dhp/oms/ds/jobhandler/SampleXxlJob.java`
- **配置文件**: `oms-data-schedule/src/main/resources/application.yaml`

## 📦 商品数据同步任务

| 任务名称 | JobHandler | 功能描述 | 调用的领星ERP接口 | 同步频率建议 |
|---------|------------|----------|-------------------|--------------|
| **SPU同步任务** | `syncProductSpu` | 同步领星ERP的SPU（标准产品单元）数据 | • `/erp/sc/routing/storage/spu/spuList`<br/>• `/erp/sc/routing/storage/spu/info` | 每小时执行 |
| **SKU同步任务** | `syncProductSku` | 同步领星ERP的SKU（库存保持单元）数据 | • `/erp/sc/routing/data/local_inventory/productList`<br/>• `/erp/sc/routing/data/local_inventory/batchGetProductInfo` | 每30分钟执行 |

### 商品同步详情
- **SPU同步**: 包含基础信息、清关费用、报关信息、采购信息、规格信息
- **SKU同步**: 包含基础信息、清关费用、报关信息、采购信息、采购项目、规格信息、规格单项
- **数据处理**: 支持增量更新、批量插入、逻辑删除
- **多租户支持**: 按租户隔离同步数据

## 🛒 订单数据同步任务

### MWS订单同步（亚马逊平台订单）
| 任务名称 | JobHandler | 功能描述 | 调用的领星ERP接口 | 同步频率建议 |
|---------|------------|----------|-------------------|--------------|
| **MWS订单全量同步** | `syncMwsOrderFull` | 全量同步亚马逊平台订单数据（过去2年） | • `/erp/sc/data/mws/orders`<br/>• `/erp/sc/data/mws/orderDetail` | 每日凌晨执行 |
| **MWS订单增量同步** | `syncMwsOrderIncr` | 增量同步亚马逊平台订单数据（过去30分钟） | • `/erp/sc/data/mws/orders`<br/>• `/erp/sc/data/mws/orderDetail` | 每30分钟执行 |

### FBM订单同步（亚马逊自发货订单）
| 任务名称 | JobHandler | 功能描述 | 调用的领星ERP接口 | 同步频率建议 |
|---------|------------|----------|-------------------|--------------|
| **FBM订单全量同步** | `syncFbmOrderFull` | 全量同步亚马逊自发货订单数据（过去2年） | • `/erp/sc/routing/order/Order/getOrderList`<br/>• `/erp/sc/routing/order/Order/getOrderDetail` | 每日凌晨执行 |
| **FBM订单增量同步** | `syncFbmOrderIncr` | 增量同步亚马逊自发货订单数据（过去30分钟） | • `/erp/sc/routing/order/Order/getOrderList`<br/>• `/erp/sc/routing/order/Order/getOrderDetail` | 每30分钟执行 |

### 多平台订单同步
| 任务名称 | JobHandler | 功能描述 | 调用的领星ERP接口 | 同步频率建议 |
|---------|------------|----------|-------------------|--------------|
| **多平台订单全量同步** | `syncMpOrderFull` | 全量同步多平台订单数据（过去1个月） | • `/pb/mp/order/v2/list` | 每日凌晨执行 |
| **多平台订单增量同步** | `syncMpOrderIncr` | 增量同步多平台订单数据（过去30分钟） | • `/pb/mp/order/v2/list` | 每30分钟执行 |

## 🔧 售后数据同步任务

### 售后退货订单同步
| 任务名称 | JobHandler | 功能描述 | 调用的领星ERP接口 | 同步频率建议 |
|---------|------------|----------|-------------------|--------------|
| **售后退货全量同步** | `syncAfsOrderRefundFull` | 全量同步FBA退货订单数据 | • `/erp/sc/data/mws_report/refundOrders` | 每日凌晨执行 |
| **售后退货增量同步** | `syncAfsOrderRefundIncr` | 增量同步FBA退货订单数据 | • `/erp/sc/data/mws_report/refundOrders` | 每小时执行 |

### 售后换货订单同步
| 任务名称 | JobHandler | 功能描述 | 调用的领星ERP接口 | 同步频率建议 |
|---------|------------|----------|-------------------|--------------|
| **售后换货全量同步** | `syncAfsOrderReturnFull` | 全量同步FBA换货订单数据 | • `/erp/sc/routing/data/order/fbaExchangeOrderList` | 每日凌晨执行 |
| **售后换货增量同步** | `syncAfsOrderReturnIncr` | 增量同步FBA换货订单数据 | • `/erp/sc/routing/data/order/fbaExchangeOrderList` | 每小时执行 |

### 售后订单同步
| 任务名称 | JobHandler | 功能描述 | 调用的领星ERP接口 | 同步频率建议 |
|---------|------------|----------|-------------------|--------------|
| **售后订单全量同步** | `syncAfsOrderFull` | 全量同步售后订单数据 | • `/erp/sc/routing/amzod/order/afterSaleList` | 每日凌晨执行 |
| **售后订单增量同步** | `syncAfsOrderIncr` | 增量同步售后订单数据 | • `/erp/sc/routing/amzod/order/afterSaleList` | 每小时执行 |

## 📊 任务调用的领星ERP接口汇总

### 商品管理相关接口（4个）
| 接口路径 | 调用任务 | 功能描述 | API文档 |
|---------|----------|----------|---------|
| `/erp/sc/routing/storage/spu/spuList` | syncProductSpu | 获取SPU列表 | [文档](https://apidoc.lingxing.com/#/docs/Product/spuList) |
| `/erp/sc/routing/storage/spu/info` | syncProductSpu | 获取SPU详情 | [文档](https://apidoc.lingxing.com/#/docs/Product/spuInfo) |
| `/erp/sc/routing/data/local_inventory/productList` | syncProductSku | 查询本地产品列表 | [文档](https://apidoc.lingxing.com/#/docs/Product/ProductLists) |
| `/erp/sc/routing/data/local_inventory/batchGetProductInfo` | syncProductSku | 批量获取产品详情 | [文档](https://apidoc.lingxing.com/#/docs/Product/batchGetProductInfo) |

### 订单管理相关接口（5个）
| 接口路径 | 调用任务 | 功能描述 | API文档 |
|---------|----------|----------|---------|
| `/erp/sc/data/mws/orders` | syncMwsOrderFull<br/>syncMwsOrderIncr | 查询亚马逊平台订单列表 | [文档](https://apidoc.lingxing.com/#/docs/Sale/Orderlists) |
| `/erp/sc/data/mws/orderDetail` | syncMwsOrderFull<br/>syncMwsOrderIncr | 查询亚马逊平台订单详情 | [文档](https://apidoc.lingxing.com/#/docs/Sale/OrderDetail) |
| `/erp/sc/routing/order/Order/getOrderList` | syncFbmOrderFull<br/>syncFbmOrderIncr | 查询亚马逊自发货订单列表 | [文档](https://apidoc.lingxing.com/#/docs/Sale/FBMOrderList) |
| `/erp/sc/routing/order/Order/getOrderDetail` | syncFbmOrderFull<br/>syncFbmOrderIncr | 查询亚马逊自发货订单详情 | [文档](https://apidoc.lingxing.com/#/docs/Sale/FBMOrderDetail) |
| `/pb/mp/order/v2/list` | syncMpOrderFull<br/>syncMpOrderIncr | 查询多平台订单管理列表 | [文档](https://apidoc.lingxing.com/#/docs/MultiPlatform/V2/MultiPlatOrderV2) |

### 售后服务相关接口（3个）
| 接口路径 | 调用任务 | 功能描述 | API文档 |
|---------|----------|----------|---------|
| `/erp/sc/data/mws_report/refundOrders` | syncAfsOrderRefundFull<br/>syncAfsOrderRefundIncr | 查询亚马逊FBA退货订单 | [文档](https://apidoc.lingxing.com/#/docs/SourceData/RefundOrders) |
| `/erp/sc/routing/data/order/fbaExchangeOrderList` | syncAfsOrderReturnFull<br/>syncAfsOrderReturnIncr | 查询亚马逊FBA换货订单 | [文档](https://apidoc.lingxing.com/#/docs/SourceData/fbaExchangeOrderList) |
| `/erp/sc/routing/amzod/order/afterSaleList` | syncAfsOrderFull<br/>syncAfsOrderIncr | 查询售后订单列表 | [文档](https://apidoc.lingxing.com/#/docs/Sale/afterSaleList) |

## 🏗️ 技术架构说明

### 核心组件
| 组件 | 代码路径 | 功能说明 |
|------|----------|----------|
| **XXL-Job配置** | `oms-data-schedule/src/main/java/com/dhp/oms/ds/config/XxlJobConfig.java` | XXL-Job执行器配置 |
| **任务处理器** | `oms-data-schedule/src/main/java/com/dhp/oms/ds/jobhandler/SampleXxlJob.java` | 定时任务具体实现 |
| **定时服务** | `oms-data-schedule/src/main/java/com/dhp/oms/ds/infrastructure/service/CronService.java` | 定时任务业务逻辑 |
| **服务实现** | `oms-data-schedule/src/main/java/com/dhp/oms/ds/infrastructure/service/impl/CronServiceImpl.java` | 具体业务实现 |
| **常量定义** | `oms-data-schedule/src/main/java/com/dhp/oms/ds/constant/CommonConstants.java` | 服务常量定义 |

