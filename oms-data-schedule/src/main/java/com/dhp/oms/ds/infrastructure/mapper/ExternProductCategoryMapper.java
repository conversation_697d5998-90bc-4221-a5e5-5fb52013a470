package com.dhp.oms.ds.infrastructure.mapper;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.dhp.oms.framework.core.entity.extern.ExternProductCategory;
import com.dhp.oms.framework.core.mapper.AbstractBaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 产品分类
 */
@Mapper
public interface ExternProductCategoryMapper extends AbstractBaseMapper<ExternProductCategory> {

    default Boolean saveOrUpdate(List<ExternProductCategory> list) {
        if (ObjectUtil.isEmpty(list)) {
            return true;
        }
        // 按 cid 分组检查存在性
        Map<Long, ExternProductCategory> dbMap = list.stream()
                .collect(Collectors.toMap(ExternProductCategory::getCid, val -> val));

        // 查询已存在的记录
        List<ExternProductCategory> existList = new LambdaQueryChainWrapper<>(this)
                .in(ExternProductCategory::getCid, dbMap.keySet())
                .list();

        // 分离插入和更新数据
        List<ExternProductCategory> toInsert = new ArrayList<>();
        List<ExternProductCategory> toUpdate = new ArrayList<>();

        for (ExternProductCategory item : list) {
            Optional<ExternProductCategory> any = existList.stream()
                    .filter(e -> e.getCid().equals(item.getCid())).findAny();
            if (any.isPresent()) {
                // 为id赋值
                item.setId(any.get().getId());
                toUpdate.add(item);
            } else {
                toInsert.add(item);
            }
        }

        // 执行批量操作
        if (!toInsert.isEmpty()) {
            insertBatch(toInsert, toInsert.size());
        }
        if (!toUpdate.isEmpty()) {
            updateBatchById(toUpdate);
        }
        return true;
    }

    default ExternProductCategory getByTitle(String title) {
        return new LambdaQueryChainWrapper<>(this)
                .eq(ExternProductCategory::getTitle, title)
                .one();
    }
}


