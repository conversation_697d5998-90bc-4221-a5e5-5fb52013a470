package com.dhp.oms.ds.infrastructure.mapper.purchase;

import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.dhp.oms.framework.core.entity.ops.purchase.PurchaseRequest;
import com.dhp.oms.framework.core.mapper.AbstractBaseMapper;
import org.apache.ibatis.annotations.Mapper;

/**
 * <p>
 * 采购-申购单 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-20
 */
@Mapper
public interface PurchaseRequestMapper extends AbstractBaseMapper<PurchaseRequest> {

    default PurchaseRequest findByPggsn(Long tenantId, String ppgSn) {
        return new LambdaQueryChainWrapper<>(this)
                .eq(PurchaseRequest::getTenantId, tenantId)
                .eq(PurchaseRequest::getDeleted, Boolean.FALSE)
                .eq(PurchaseRequest::getPpgSn, ppgSn)
                .list().stream().findFirst().orElse(null);
    }

}
