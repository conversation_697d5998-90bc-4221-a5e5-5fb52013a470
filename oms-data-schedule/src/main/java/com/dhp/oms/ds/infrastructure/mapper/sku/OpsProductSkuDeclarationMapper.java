package com.dhp.oms.ds.infrastructure.mapper.sku;

import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import com.dhp.oms.framework.context.user.OpsUserContext;
import com.dhp.oms.framework.core.entity.ops.sku.OpsProductSkuClearanceFee;
import com.dhp.oms.framework.core.entity.ops.sku.OpsProductSkuDeclaration;
import com.dhp.oms.framework.core.mapper.AbstractBaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface OpsProductSkuDeclarationMapper extends AbstractBaseMapper<OpsProductSkuDeclaration> {

    default Boolean deleteByNotIdList(List<String> notInSkuIdList) {
        return new LambdaUpdateChainWrapper<>(this)
                .notIn(OpsProductSkuDeclaration::getSkuId, notInSkuIdList)
                .eq(OpsProductSkuDeclaration::getTenantId, OpsUserContext.getTenantId())
                .set(OpsProductSkuDeclaration::getDeleted, Boolean.TRUE)
                .update();
    }


}
