package com.dhp.oms.ds.infrastructure.mapper;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.dhp.oms.framework.core.entity.ops.OpsTenant;
import com.dhp.oms.framework.core.mapper.AbstractBaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-10
 */
@Mapper
public interface OpsTenantMapper extends AbstractBaseMapper<OpsTenant> {

    default List<OpsTenant> listAll() {
        return selectList(Wrappers.lambdaQuery(OpsTenant.class));
    }

}
