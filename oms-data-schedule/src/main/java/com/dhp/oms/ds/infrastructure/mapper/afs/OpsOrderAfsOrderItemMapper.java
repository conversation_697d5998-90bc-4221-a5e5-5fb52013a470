package com.dhp.oms.ds.infrastructure.mapper.afs;

import com.dhp.oms.framework.context.user.OpsUserContext;
import com.dhp.oms.framework.core.entity.ops.afs.OpsOrderAfsOrderItem;
import com.dhp.oms.framework.core.mapper.AbstractBaseMapper;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.ibatis.annotations.Mapper;

import java.util.Collections;
import java.util.List;

@Mapper
public interface OpsOrderAfsOrderItemMapper extends AbstractBaseMapper<OpsOrderAfsOrderItem> {

    default List<OpsOrderAfsOrderItem> listByOrderNos(List<String> orderNoList) {
        if (CollectionUtils.isEmpty(orderNoList)) {
            return Collections.emptyList();
        }
        return chainQuery()
                .eq(OpsOrderAfsOrderItem::getTenantId, OpsUserContext.getTenantId())
                .in(OpsOrderAfsOrderItem::getAfs<PERSON>rder<PERSON>o, orderNoList)
                .list();
    }

}
