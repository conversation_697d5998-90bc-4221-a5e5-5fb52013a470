package com.dhp.oms.ds.infrastructure.mapper.sku;

import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import com.dhp.oms.framework.context.user.OpsUserContext;
import com.dhp.oms.framework.core.entity.ops.sku.OpsProductSkuSpecificationInfo;
import com.dhp.oms.framework.core.entity.ops.sku.OpsProductSkuSpecificationSingle;
import com.dhp.oms.framework.core.mapper.AbstractBaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface OpsProductSkuSpecificationSingleMapper extends AbstractBaseMapper<OpsProductSkuSpecificationSingle> {

    default Boolean deleteByNotIdList(List<String> notInSkuIdList) {
        return new LambdaUpdateChainWrapper<>(this)
                .notIn(OpsProductSkuSpecificationSingle::getSkuId, notInSkuIdList)
                .eq(OpsProductSkuSpecificationSingle::getTenantId, OpsUserContext.getTenantId())
                .set(OpsProductSkuSpecificationSingle::getDeleted, Boolean.TRUE)
                .update();
    }

}
