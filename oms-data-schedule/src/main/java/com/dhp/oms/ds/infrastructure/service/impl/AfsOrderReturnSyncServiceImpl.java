package com.dhp.oms.ds.infrastructure.service.impl;


import com.dhp.oms.ds.infrastructure.service.AbstractAfsReturnOrderSyncService;
import com.dhp.oms.framework.context.user.OpsUserContext;
import com.dhp.oms.framework.core.entity.ops.afs.OpsOrderAfsReturnOrder;
import com.dhp.oms.framework.core.entity.ops.afs.OpsOrderAfsReturnOrderItem;
import com.dhp.oms.framework.utils.CommonUtils;
import com.dhp.oms.framework.utils.SnowflakeIdWorkerUtils;
import com.dhp.oms.third.api.common.ExternApiResult;
import com.dhp.oms.third.api.module.afs.AfsApi;
import com.dhp.oms.third.api.module.afs.request.DataOrderFbaExchangeOrderListRequest;
import com.dhp.oms.third.api.module.afs.response.DataOrderFbaExchangeOrderListResponse;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.BiConsumer;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service("afsOrderReturnSyncServiceImpl")
public class AfsOrderReturnSyncServiceImpl extends AbstractAfsReturnOrderSyncService<DataOrderFbaExchangeOrderListRequest, DataOrderFbaExchangeOrderListResponse, DataOrderFbaExchangeOrderListResponse, DataOrderFbaExchangeOrderListResponse> {

    @Resource
    private AfsApi afsApi;

    @Override
    protected DataOrderFbaExchangeOrderListRequest newOrderListRequestInstance(List<String> partitionSidList) {
        DataOrderFbaExchangeOrderListRequest request = new DataOrderFbaExchangeOrderListRequest();
        request.setSid(partitionSidList.get(0));
        return request;
    }

    @Override
    protected boolean firstPage(DataOrderFbaExchangeOrderListRequest dataOrderFbaExchangeOrderListRequest) {
        return dataOrderFbaExchangeOrderListRequest.getOffset() == 0;
    }

    @Override
    protected boolean hasNextPage(DataOrderFbaExchangeOrderListRequest dataOrderFbaExchangeOrderListRequest, Long total) {
        return dataOrderFbaExchangeOrderListRequest.getOffset() < total;
    }

    @Override
    protected void setNextPage(DataOrderFbaExchangeOrderListRequest dataOrderFbaExchangeOrderListRequest) {
        dataOrderFbaExchangeOrderListRequest.setOffset(dataOrderFbaExchangeOrderListRequest.getOffset() + dataOrderFbaExchangeOrderListRequest.getLength());
    }

    @Override
    protected List<DataOrderFbaExchangeOrderListResponse> getOrderList(ExternApiResult<?> result) {
        return Optional.ofNullable(result)
                .map(item -> (ExternApiResult<List<DataOrderFbaExchangeOrderListResponse>>) item)
                .map(ExternApiResult::getData)
                .orElse(Collections.emptyList());
    }

    @Override
    protected Function<DataOrderFbaExchangeOrderListResponse, String> obtainGetOrderIdFunc() {
        return DataOrderFbaExchangeOrderListResponse::getReplacement_amazon_order_id;
    }

    @Override
    protected BiConsumer<DataOrderFbaExchangeOrderListResponse, Long> obtainSetBizOrderIdFunc() {
        return DataOrderFbaExchangeOrderListResponse::setBizOrderId;
    }

    @Override
    protected Function<DataOrderFbaExchangeOrderListResponse, Long> obtainGetBizOrderIdFunc() {
        return DataOrderFbaExchangeOrderListResponse::getBizOrderId;
    }

    @Override
    protected Function<DataOrderFbaExchangeOrderListResponse, String> obtainGetOrderItemIdFunc() {
        return DataOrderFbaExchangeOrderListResponse::getSeller_sku;
    }

    @Override
    protected BiConsumer<DataOrderFbaExchangeOrderListResponse, Long> obtainSetBizOrderItemIdFunc() {
        return DataOrderFbaExchangeOrderListResponse::setBizOrderItemId;
    }

    @Override
    protected Function<DataOrderFbaExchangeOrderListResponse, Long> obtainGetBizOrderItemIdFunc() {
        return DataOrderFbaExchangeOrderListResponse::getBizOrderItemId;
    }

    @Override
    protected List<DataOrderFbaExchangeOrderListResponse> obtainOrderSkuList(DataOrderFbaExchangeOrderListResponse dataOrderFbaExchangeOrderListResponse, DataOrderFbaExchangeOrderListResponse dataOrderFbaExchangeOrderListResponse2) {
        return Collections.singletonList(dataOrderFbaExchangeOrderListResponse);
    }

    @Override
    protected ExternApiResult<?> getOrderResponse(DataOrderFbaExchangeOrderListRequest dataOrderFbaExchangeOrderListRequest, LocalDateTime beginTime, LocalDateTime endTime) {
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        dataOrderFbaExchangeOrderListRequest.setStart_date(dateTimeFormatter.format(beginTime));
        dataOrderFbaExchangeOrderListRequest.setEnd_date(dateTimeFormatter.format(endTime));
        return afsApi.dataOrderFbaExchangeOrderList(dataOrderFbaExchangeOrderListRequest);
    }

    @Override
    protected Map<String, DataOrderFbaExchangeOrderListResponse> getOrderDetailResponse(List<DataOrderFbaExchangeOrderListResponse> orderList) {
        return orderList.stream()
                .collect(Collectors.toMap(
                        obtainGetOrderIdFunc(),
                        Function.identity(),
                        (pre, next) -> next));
    }

    @Override
    protected void buildInsertOrder(DataOrderFbaExchangeOrderListRequest dataOrderFbaExchangeOrderListRequest, DataOrderFbaExchangeOrderListResponse dataOrderFbaExchangeOrderListResponse, DataOrderFbaExchangeOrderListResponse dataOrderFbaExchangeOrderListResponse2, OpsOrderAfsReturnOrder opsOrderAfsReturnOrder) {
        opsOrderAfsReturnOrder.setId(SnowflakeIdWorkerUtils.generateId());
        opsOrderAfsReturnOrder.setTenantId(OpsUserContext.getTenantId());
        opsOrderAfsReturnOrder.setSid(dataOrderFbaExchangeOrderListResponse.getSid());
        opsOrderAfsReturnOrder.setPlatform("Amazon");
        opsOrderAfsReturnOrder.setSalePlatform("Amazon");
        opsOrderAfsReturnOrder.setPlatformOrderNo(dataOrderFbaExchangeOrderListResponse.getReplacement_amazon_order_id());
        opsOrderAfsReturnOrder.setOmsOrderNo(null);
        opsOrderAfsReturnOrder.setSaleOrderNo(dataOrderFbaExchangeOrderListResponse.getOriginal_amazon_order_id());
        opsOrderAfsReturnOrder.setReferenceNo(null);
        opsOrderAfsReturnOrder.setCreateDate(CommonUtils.convertStringToLocalDateTime(dataOrderFbaExchangeOrderListResponse.getShipment_date(), CommonUtils.DATETIME_FORMATTER, ZoneId.of("UTC"), null));
        opsOrderAfsReturnOrder.setCreateBy(OpsUserContext.getTenantId());
        opsOrderAfsReturnOrder.setCreateTime(LocalDateTime.now());
        buildUpdateOrder(dataOrderFbaExchangeOrderListResponse, dataOrderFbaExchangeOrderListResponse, opsOrderAfsReturnOrder);
    }

    @Override
    protected void buildUpdateOrder(DataOrderFbaExchangeOrderListResponse dataOrderFbaExchangeOrderListResponse, DataOrderFbaExchangeOrderListResponse dataOrderFbaExchangeOrderListResponse2, OpsOrderAfsReturnOrder opsOrderAfsReturnOrder) {
        opsOrderAfsReturnOrder.setUpdateBy(OpsUserContext.getTenantId());
        opsOrderAfsReturnOrder.setUpdateTime(LocalDateTime.now());
    }

    @Override
    protected void buildInsertOrderSku(DataOrderFbaExchangeOrderListResponse dataOrderFbaExchangeOrderListResponse, DataOrderFbaExchangeOrderListResponse dataOrderFbaExchangeOrderListResponse2, DataOrderFbaExchangeOrderListResponse dataOrderFbaExchangeOrderListResponse3, OpsOrderAfsReturnOrder opsOrderAfsReturnOrder, OpsOrderAfsReturnOrderItem opsOrderAfsReturnOrderItem) {
        opsOrderAfsReturnOrderItem.setId(SnowflakeIdWorkerUtils.generateId());
        opsOrderAfsReturnOrderItem.setTenantId(OpsUserContext.getTenantId());
        opsOrderAfsReturnOrderItem.setPlatformOrderNo(dataOrderFbaExchangeOrderListResponse.getOriginal_amazon_order_id());
        opsOrderAfsReturnOrderItem.setMsku(dataOrderFbaExchangeOrderListResponse.getSeller_sku());
        opsOrderAfsReturnOrderItem.setSku(dataOrderFbaExchangeOrderListResponse.getSeller_sku());
        opsOrderAfsReturnOrderItem.setProductName(null);
        opsOrderAfsReturnOrderItem.setCreateBy(OpsUserContext.getTenantId());
        opsOrderAfsReturnOrderItem.setCreateTime(LocalDateTime.now());
        buildUpdateOrderSku(dataOrderFbaExchangeOrderListResponse, dataOrderFbaExchangeOrderListResponse, dataOrderFbaExchangeOrderListResponse, opsOrderAfsReturnOrder, opsOrderAfsReturnOrderItem);
    }

    @Override
    protected void buildUpdateOrderSku(DataOrderFbaExchangeOrderListResponse dataOrderFbaExchangeOrderListResponse, DataOrderFbaExchangeOrderListResponse dataOrderFbaExchangeOrderListResponse2, DataOrderFbaExchangeOrderListResponse dataOrderFbaExchangeOrderListResponse3, OpsOrderAfsReturnOrder opsOrderAfsReturnOrder, OpsOrderAfsReturnOrderItem opsOrderAfsReturnOrderItem) {
        opsOrderAfsReturnOrderItem.setQuantity(dataOrderFbaExchangeOrderListResponse.getQuantity());
        opsOrderAfsReturnOrderItem.setUpdateBy(OpsUserContext.getTenantId());
        opsOrderAfsReturnOrderItem.setUpdateTime(LocalDateTime.now());
    }

}
