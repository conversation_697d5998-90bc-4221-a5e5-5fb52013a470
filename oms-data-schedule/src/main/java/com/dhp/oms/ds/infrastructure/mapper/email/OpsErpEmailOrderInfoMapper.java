package com.dhp.oms.ds.infrastructure.mapper.email;

import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.dhp.oms.framework.core.entity.ops.email.OpsErpEmailOrderInfo;
import com.dhp.oms.framework.core.mapper.AbstractBaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface OpsErpEmailOrderInfoMapper extends AbstractBaseMapper<OpsErpEmailOrderInfo> {
   default List<OpsErpEmailOrderInfo> selectByUuidOrderIds(List<String> uuidOrderIds){
       return new LambdaQueryChainWrapper<>(this)
               .in(OpsErpEmailOrderInfo::getUuidOrderId,uuidOrderIds)
               .list();
   }

    default List<OpsErpEmailOrderInfo>  searchListByEmailOrderId(String orderId){
       return new LambdaQueryChainWrapper<>(this).eq(OpsErpEmailOrderInfo::getEmailOrderId,orderId).list();
    }
}
