package com.dhp.oms.ds.infrastructure.service.impl;


import com.alibaba.fastjson2.JSON;
import com.dhp.oms.ds.infrastructure.service.AbstractAfsRefundOrderSyncService;
import com.dhp.oms.framework.context.user.OpsUserContext;
import com.dhp.oms.framework.core.entity.ops.afs.OpsOrderAfsRefundOrder;
import com.dhp.oms.framework.core.entity.ops.afs.OpsOrderAfsRefundOrderItem;
import com.dhp.oms.framework.core.entity.ops.store.StoreInfo;
import com.dhp.oms.framework.utils.CommonUtils;
import com.dhp.oms.framework.utils.SnowflakeIdWorkerUtils;
import com.dhp.oms.third.api.common.ExternApiResult;
import com.dhp.oms.third.api.module.afs.AfsApi;
import com.dhp.oms.third.api.module.afs.request.DataMwsReportRefundOrdersRequest;
import com.dhp.oms.third.api.module.afs.response.DataMwsReportRefundOrdersResponse;
import com.google.common.collect.Lists;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.BiConsumer;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service("afsOrderRefundSyncServiceImpl")
public class AfsOrderRefundSyncServiceImpl extends AbstractAfsRefundOrderSyncService<DataMwsReportRefundOrdersRequest, DataMwsReportRefundOrdersResponse, DataMwsReportRefundOrdersResponse, DataMwsReportRefundOrdersResponse> {

    @Resource
    private AfsApi afsApi;

    @Override
    protected DataMwsReportRefundOrdersRequest newOrderListRequestInstance(List<String> partitionSidList) {
        DataMwsReportRefundOrdersRequest request = new DataMwsReportRefundOrdersRequest();
        request.setSid(partitionSidList.get(0));
        return request;
    }

    @Override
    protected boolean firstPage(DataMwsReportRefundOrdersRequest dataMwsReportRefundOrdersRequest) {
        return dataMwsReportRefundOrdersRequest.getOffset() == 0;
    }

    @Override
    protected boolean hasNextPage(DataMwsReportRefundOrdersRequest dataMwsReportRefundOrdersRequest, Long total) {
        return dataMwsReportRefundOrdersRequest.getOffset() < total;
    }

    @Override
    protected void setNextPage(DataMwsReportRefundOrdersRequest dataMwsReportRefundOrdersRequest) {
        dataMwsReportRefundOrdersRequest.setOffset(dataMwsReportRefundOrdersRequest.getOffset() + dataMwsReportRefundOrdersRequest.getLength());
    }

    @Override
    protected List<DataMwsReportRefundOrdersResponse> getOrderList(ExternApiResult<?> result) {
        return Optional.ofNullable(result)
                .map(item -> (ExternApiResult<List<DataMwsReportRefundOrdersResponse>>) item)
                .map(ExternApiResult::getData)
                .orElse(Collections.emptyList());
    }

    @Override
    protected Function<DataMwsReportRefundOrdersResponse, String> obtainGetOrderIdFunc() {
        return DataMwsReportRefundOrdersResponse::getOrder_id;
    }

    @Override
    protected BiConsumer<DataMwsReportRefundOrdersResponse, Long> obtainSetBizOrderIdFunc() {
        return DataMwsReportRefundOrdersResponse::setBizOrderId;
    }

    @Override
    protected Function<DataMwsReportRefundOrdersResponse, Long> obtainGetBizOrderIdFunc() {
        return DataMwsReportRefundOrdersResponse::getBizOrderId;
    }

    @Override
    protected Function<DataMwsReportRefundOrdersResponse, String> obtainGetOrderItemIdFunc() {
        return DataMwsReportRefundOrdersResponse::getSku;
    }

    @Override
    protected BiConsumer<DataMwsReportRefundOrdersResponse, Long> obtainSetBizOrderItemIdFunc() {
        return DataMwsReportRefundOrdersResponse::setBizOrderItemId;
    }

    @Override
    protected Function<DataMwsReportRefundOrdersResponse, Long> obtainGetBizOrderItemIdFunc() {
        return DataMwsReportRefundOrdersResponse::getBizOrderItemId;
    }

    @Override
    protected List<DataMwsReportRefundOrdersResponse> obtainOrderSkuList(DataMwsReportRefundOrdersResponse dataMwsReportRefundOrdersResponse, DataMwsReportRefundOrdersResponse dataMwsReportRefundOrdersResponse2) {
        return Collections.singletonList(dataMwsReportRefundOrdersResponse);
    }

    @Override
    protected ExternApiResult<?> getOrderResponse(DataMwsReportRefundOrdersRequest dataMwsReportRefundOrdersRequest, LocalDateTime beginTime, LocalDateTime endTime) {
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        dataMwsReportRefundOrdersRequest.setStart_date(dateTimeFormatter.format(beginTime));
        dataMwsReportRefundOrdersRequest.setEnd_date(dateTimeFormatter.format(endTime));
        return afsApi.dataMwsReportRefundOrders(dataMwsReportRefundOrdersRequest);
    }

    @Override
    protected Map<String, DataMwsReportRefundOrdersResponse> getOrderDetailResponse(List<DataMwsReportRefundOrdersResponse> orderList) {
        return orderList.stream()
                .collect(Collectors.toMap(
                        obtainGetOrderIdFunc(),
                        Function.identity(),
                        (pre, next) -> next));
    }

    @Override
    protected void buildInsertOrder(DataMwsReportRefundOrdersRequest dataMwsReportRefundOrdersRequest, DataMwsReportRefundOrdersResponse dataMwsReportRefundOrdersResponse, DataMwsReportRefundOrdersResponse dataMwsReportRefundOrdersResponse2, OpsOrderAfsRefundOrder opsOrderAfsRefundOrder) {
        opsOrderAfsRefundOrder.setId(SnowflakeIdWorkerUtils.generateId());
        opsOrderAfsRefundOrder.setTenantId(OpsUserContext.getTenantId());
        opsOrderAfsRefundOrder.setSid(dataMwsReportRefundOrdersResponse.getSid());
        opsOrderAfsRefundOrder.setPlatform("Amazon");
        opsOrderAfsRefundOrder.setSalePlatform("Amazon");
        opsOrderAfsRefundOrder.setPlatformOrderNo(dataMwsReportRefundOrdersResponse.getOrder_id());
        opsOrderAfsRefundOrder.setOmsOrderNo(null);
        opsOrderAfsRefundOrder.setSaleOrderNo(null);
        opsOrderAfsRefundOrder.setReferenceNo(null);
        opsOrderAfsRefundOrder.setCurrency(null);
        opsOrderAfsRefundOrder.setRemark(dataMwsReportRefundOrdersResponse.getRemark());
        opsOrderAfsRefundOrder.setCreateDate(CommonUtils.convertStringToLocalDateTime(dataMwsReportRefundOrdersResponse.getReturn_date(), DateTimeFormatter.ISO_OFFSET_DATE_TIME, ZoneId.of("UTC"), null));
        opsOrderAfsRefundOrder.setStatus(dataMwsReportRefundOrdersResponse.getStatus());
        opsOrderAfsRefundOrder.setCreateBy(OpsUserContext.getTenantId());
        opsOrderAfsRefundOrder.setCreateTime(LocalDateTime.now());
        buildUpdateOrder(dataMwsReportRefundOrdersResponse, dataMwsReportRefundOrdersResponse, opsOrderAfsRefundOrder);
    }

    @Override
    protected void buildUpdateOrder(DataMwsReportRefundOrdersResponse dataMwsReportRefundOrdersResponse, DataMwsReportRefundOrdersResponse dataMwsReportRefundOrdersResponse2, OpsOrderAfsRefundOrder opsOrderAfsRefundOrder) {
        opsOrderAfsRefundOrder.setUpdateBy(OpsUserContext.getTenantId());
        opsOrderAfsRefundOrder.setUpdateTime(LocalDateTime.now());
    }

    @Override
    protected void buildInsertOrderSku(DataMwsReportRefundOrdersResponse dataMwsReportRefundOrdersResponse, DataMwsReportRefundOrdersResponse dataMwsReportRefundOrdersResponse2, DataMwsReportRefundOrdersResponse dataMwsReportRefundOrdersResponse3, OpsOrderAfsRefundOrder opsOrderAfsRefundOrder, OpsOrderAfsRefundOrderItem opsOrderAfsRefundOrderItem) {
        opsOrderAfsRefundOrderItem.setId(SnowflakeIdWorkerUtils.generateId());
        opsOrderAfsRefundOrderItem.setTenantId(OpsUserContext.getTenantId());
        opsOrderAfsRefundOrderItem.setPlatformOrderNo(dataMwsReportRefundOrdersResponse.getOrder_id());
        opsOrderAfsRefundOrderItem.setMsku(dataMwsReportRefundOrdersResponse.getFnsku());
        opsOrderAfsRefundOrderItem.setSku(dataMwsReportRefundOrdersResponse.getSku());
        opsOrderAfsRefundOrderItem.setProductName(dataMwsReportRefundOrdersResponse.getProduct_name());
        opsOrderAfsRefundOrderItem.setCreateBy(OpsUserContext.getTenantId());
        opsOrderAfsRefundOrderItem.setCreateTime(LocalDateTime.now());
        buildUpdateOrderSku(dataMwsReportRefundOrdersResponse, dataMwsReportRefundOrdersResponse, dataMwsReportRefundOrdersResponse, opsOrderAfsRefundOrder, opsOrderAfsRefundOrderItem);
    }

    @Override
    protected void buildUpdateOrderSku(DataMwsReportRefundOrdersResponse dataMwsReportRefundOrdersResponse, DataMwsReportRefundOrdersResponse dataMwsReportRefundOrdersResponse2, DataMwsReportRefundOrdersResponse dataMwsReportRefundOrdersResponse3, OpsOrderAfsRefundOrder opsOrderAfsRefundOrder, OpsOrderAfsRefundOrderItem opsOrderAfsRefundOrderItem) {
        opsOrderAfsRefundOrderItem.setRemark(dataMwsReportRefundOrdersResponse.getRemark());
        opsOrderAfsRefundOrderItem.setQuantity(dataMwsReportRefundOrdersResponse.getQuantity());
        opsOrderAfsRefundOrderItem.setUpdateBy(OpsUserContext.getTenantId());
        opsOrderAfsRefundOrderItem.setUpdateTime(LocalDateTime.now());
    }

}
