package com.dhp.oms.ds.infrastructure.service.impl;

import com.dhp.oms.ds.infrastructure.converter.PurchaseApiConverter;
import com.dhp.oms.ds.infrastructure.generator.PurchaseReuqestNumberGenerator;
import com.dhp.oms.ds.infrastructure.mapper.purchase.PurchasePlanMapper;
import com.dhp.oms.ds.infrastructure.mapper.purchase.PurchaseRequestMapper;
import com.dhp.oms.ds.infrastructure.mapper.purchase.PurchaseRequestSkuMapper;
import com.dhp.oms.ds.infrastructure.service.PurchaseSyncService;
import com.dhp.oms.framework.context.tenant.TenentContext;
import com.dhp.oms.framework.core.entity.ops.purchase.PurchasePlan;
import com.dhp.oms.framework.core.entity.ops.purchase.PurchaseRequest;
import com.dhp.oms.framework.core.entity.ops.purchase.PurchaseRequestSku;
import com.dhp.oms.framework.core.enums.ops.purchase.request.PurchaseRequestStatusEnum;
import com.dhp.oms.framework.utils.ObjectMapperUtil;
import com.dhp.oms.framework.utils.SnowflakeIdWorkerUtils;
import com.dhp.oms.third.api.common.ExternApiResult;
import com.dhp.oms.third.api.module.purchase.PurchaseApi;
import com.dhp.oms.third.api.module.purchase.request.GetPurchasePlansRequest;
import com.dhp.oms.third.api.module.purchase.response.GetPurchasePlansResponse;
import com.google.common.collect.Sets;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@RequiredArgsConstructor
@Service
public class PurchaseSyncServiceImpl implements PurchaseSyncService {

    private final PurchaseApi purchaseApi;
    private final PurchasePlanMapper purchasePlanMapper;
    private final PurchaseRequestMapper purchaseRequestMapper;
    private final PurchaseRequestSkuMapper purchaseRequestSkuMapper;
    private final PurchaseReuqestNumberGenerator purchaseReuqestNumberGenerator;

    @Override
    public void mockSync() {
        Set<String> planSns = Sets.newHashSet(
                // 批次号=PPG250514001
                "PP250514054", "PP250514055", "PP250514056", "PP250514057",
                // 批次号=PPG250514002
                "PP250514083", "PP250514084", "PP250514085",
                // Marvin2025/6/27创建的申购单号
                "PP250627001"
        );

        GetPurchasePlansRequest request = new GetPurchasePlansRequest();
        request.setOffset(0);
        request.setLength(500);
        request.setPlan_sns(planSns);

        ExternApiResult<List<GetPurchasePlansResponse>> rs = purchaseApi.getPurchasePlans(request);
        if (!rs.isSuccess()) {
            log.info("error response :{}", ObjectMapperUtil.obj2Json(rs));
            return;
        }

        Long tenantId = TenentContext.MOCK_TENANT_ID;
        rs.getData().stream().collect(Collectors.groupingBy(GetPurchasePlansResponse::getPpg_sn))
                .forEach((k, v) -> mock(tenantId, k, v));
    }

    private void mock(Long tenantId, String ppgSn, List<GetPurchasePlansResponse> responseList) {
        Set<String> planSns = responseList.stream().map(GetPurchasePlansResponse::getPlan_sn).collect(Collectors.toSet());
        Map<String, PurchasePlan> map = purchasePlanMapper.findByPlanSns(tenantId, ppgSn, planSns).stream()
                .collect(Collectors.toMap(PurchasePlan::getPlanSn, Function.identity(), (v1, v2) -> v1));

        List<PurchasePlan> planList = responseList.stream().filter(v -> map.get(v.getPlan_sn()) == null)
                .map(PurchaseApiConverter.INSTANCE::convertPurchasePlan)
                .peek(v -> {
                    v.setId(SnowflakeIdWorkerUtils.generateId());
                    v.setTenantId(tenantId);
                })
                .toList();
        if (CollectionUtils.isNotEmpty(planList)) {
            // 已同步的不管
            purchasePlanMapper.saveBatch(planList);
        }

        PurchaseRequest purchaseRequest = purchaseRequestMapper.findByPggsn(tenantId, ppgSn);
        Long requestId;
        String requestNo;
        if (purchaseRequest == null) {
            requestId = SnowflakeIdWorkerUtils.generateId();
            requestNo = purchaseReuqestNumberGenerator.generate(tenantId);

            PurchaseRequest request = new PurchaseRequest();
            request.setId(requestId);
            request.setTenantId(tenantId);
            request.setRequestNo(requestNo);
            request.setRequestStatus(PurchaseRequestStatusEnum.PENDING.getStatus());
            request.setPpgSn(ppgSn);

            // 已同步的不管
            purchaseRequestMapper.insert(request);
        } else {
            requestId = purchaseRequest.getId();
            requestNo = purchaseRequest.getRequestNo();
        }

        Map<String, PurchaseRequestSku> requestSkuMap = purchaseRequestSkuMapper.findByRequestId(tenantId, requestId).stream()
                .collect(Collectors.toMap(PurchaseRequestSku::getPlanSn, Function.identity(), (v1, v2) -> v1));
        List<PurchaseRequestSku> requestSkuList = responseList.stream().filter(v -> requestSkuMap.get(v.getPlan_sn()) == null)
                .map(PurchaseApiConverter.INSTANCE::convertPurchaseRequestSku)
                .peek(v -> {
                    v.setId(SnowflakeIdWorkerUtils.generateId());
                    v.setTenantId(tenantId);
                    v.setRequestId(requestId);
                    v.setRequestNo(requestNo);
                })
                .toList();
        if (CollectionUtils.isNotEmpty(requestSkuList)) {
            // 已同步的不管
            purchaseRequestSkuMapper.saveBatch(requestSkuList);
        }
    }

    @Override
    public void sync() {

    }

}
