package com.dhp.oms.ds.infrastructure.mapper.sku;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import com.dhp.oms.framework.context.user.OpsUserContext;
import com.dhp.oms.framework.core.entity.ops.sku.OpsProductSkuBaseInfo;
import com.dhp.oms.framework.core.mapper.AbstractBaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface OpsProductSkuBaseInfoMapper extends AbstractBaseMapper<OpsProductSkuBaseInfo> {

    default List<OpsProductSkuBaseInfo> listBySkuList(List<String> sku) {
        return selectList(Wrappers.lambdaQuery(OpsProductSkuBaseInfo.class)
                .select(OpsProductSkuBaseInfo::getId,
                        OpsProductSkuBaseInfo::getSku)
                .eq(OpsProductSkuBaseInfo::getTenantId, OpsUserContext.getTenantId())
                .in(OpsProductSkuBaseInfo::getSku, sku));
    }

    default Boolean deleteByNotIdList(List<Long> notInIdList) {
        return new LambdaUpdateChainWrapper<>(this)
                .notIn(OpsProductSkuBaseInfo::getId, notInIdList)
                .eq(OpsProductSkuBaseInfo::getTenantId, OpsUserContext.getTenantId())
                .set(OpsProductSkuBaseInfo::getDeleted, Boolean.TRUE)
                .update();
    }

}

