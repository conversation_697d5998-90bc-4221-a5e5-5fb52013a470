package com.dhp.oms.ds.infrastructure.service.impl;

import com.dhp.oms.ds.infrastructure.service.AbstractSaleOrderSyncService;
import com.dhp.oms.framework.context.user.OpsUserContext;
import com.dhp.oms.framework.core.entity.ops.order.OpsOrderSaleOrder;
import com.dhp.oms.framework.core.entity.ops.order.OpsOrderSaleOrderSku;
import com.dhp.oms.framework.utils.CommonUtils;
import com.dhp.oms.framework.utils.SnowflakeIdWorkerUtils;
import com.dhp.oms.third.api.common.ExternApiResult;
import com.dhp.oms.third.api.module.order.MpOrderApi;
import com.dhp.oms.third.api.module.order.request.MpOrderRequest;
import com.dhp.oms.third.api.module.order.response.MpOrderResponse;
import jakarta.annotation.Resource;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.BiConsumer;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service("mpOrderSyncServiceImpl")
public class MpOrderSyncServiceImpl extends AbstractSaleOrderSyncService<MpOrderRequest, MpOrderResponse.Order, MpOrderResponse.Order, MpOrderResponse.ItemInfo> {

    @Resource
    private MpOrderApi mpOrderApi;

    @Override
    protected MpOrderRequest newOrderListRequestInstance(List<String> partitionSidList) {
        return new MpOrderRequest();
    }

    @Override
    protected boolean firstPage(MpOrderRequest mpOrderRequest) {
        return mpOrderRequest.getOffset() == 0;
    }

    @Override
    protected boolean hasNextPage(MpOrderRequest mpOrderRequest, Long total) {
        return mpOrderRequest.getOffset() < total;
    }

    @Override
    protected void setNextPage(MpOrderRequest mpOrderRequest) {
        mpOrderRequest.setOffset(mpOrderRequest.getOffset() + mpOrderRequest.getLength());
    }

    @Override
    protected Long getTotal(ExternApiResult<?> result) {
        return Optional.ofNullable(result)
                .map(item -> (ExternApiResult<MpOrderResponse>) item)
                .map(ExternApiResult::getData)
                .map(MpOrderResponse::getTotal)
                .orElse(0L);
    }

    @Override
    protected List<MpOrderResponse.Order> getOrderList(ExternApiResult<?> result) {
        return Optional.ofNullable(result)
                .map(item -> (ExternApiResult<MpOrderResponse>) item)
                .map(ExternApiResult::getData)
                .map(MpOrderResponse::getList)
                .orElse(Collections.emptyList());
    }

    @Override
    protected Function<MpOrderResponse.Order, String> obtainGetOrderIdFunc() {
        return MpOrderResponse.Order::getGlobal_order_no;
    }

    @Override
    protected BiConsumer<MpOrderResponse.Order, Long> obtainSetBizOrderIdFunc() {
        return MpOrderResponse.Order::setBizOrderId;
    }

    @Override
    protected Function<MpOrderResponse.Order, Long> obtainGetBizOrderIdFunc() {
        return MpOrderResponse.Order::getBizOrderId;
    }

    @Override
    protected Function<MpOrderResponse.ItemInfo, String> obtainGetOrderItemIdFunc() {
        return MpOrderResponse.ItemInfo::getOrder_item_no;
    }

    @Override
    protected BiConsumer<MpOrderResponse.ItemInfo, Long> obtainSetBizOrderItemIdFunc() {
        return MpOrderResponse.ItemInfo::setBizOrderItemId;
    }

    @Override
    protected Function<MpOrderResponse.ItemInfo, Long> obtainGetBizOrderItemIdFunc() {
        return MpOrderResponse.ItemInfo::getBizOrderItemId;
    }

    @Override
    protected List<MpOrderResponse.ItemInfo> obtainOrderSkuList(MpOrderResponse.Order order, MpOrderResponse.Order order2) {
        return order.getItem_info();
    }

    @Override
    protected ExternApiResult<?> getOrderResponse(MpOrderRequest mpOrderRequest, LocalDateTime beginTime, LocalDateTime endTime) {
        mpOrderRequest.setDate_type("update_time");
        mpOrderRequest.setStart_time((int) beginTime.toEpochSecond(ZoneOffset.UTC));
        mpOrderRequest.setEnd_time((int) endTime.toEpochSecond(ZoneOffset.UTC));
        return mpOrderApi.orders(mpOrderRequest);
    }

    @Override
    protected Map<String, MpOrderResponse.Order> getOrderDetailResponse(List<MpOrderResponse.Order> orderList) {
        return orderList.stream()
                .collect(Collectors.toMap(
                        MpOrderResponse.Order::getGlobal_order_no,
                        Function.identity(),
                        (pre, next) -> next));
    }

    @Override
    protected void buildInsertOrder(MpOrderRequest orderListRequest, MpOrderResponse.Order order, MpOrderResponse.Order order2, OpsOrderSaleOrder opsOrderSaleOrder) {
        MpOrderResponse.PlatformInfo platformInfo = order.getPlatform_info().get(0);

        opsOrderSaleOrder.setId(SnowflakeIdWorkerUtils.generateId());
        opsOrderSaleOrder.setTenantId(OpsUserContext.getTenantId());
        opsOrderSaleOrder.setOrigin("MP");
        opsOrderSaleOrder.setSid(order.getStore_id());
        opsOrderSaleOrder.setSalesChannel(platformInfo.getStore_Country_code());
        opsOrderSaleOrder.setPlatformOrderNo(platformInfo.getPlatform_order_no());
        opsOrderSaleOrder.setReferenceNo(order.getReference_no());
        opsOrderSaleOrder.setPurchaseOrderNumber(platformInfo.getPlatform_order_name());
        opsOrderSaleOrder.setOmsOrderNo(order.getGlobal_order_no());
        opsOrderSaleOrder.setCreateBy(OpsUserContext.getTenantId());
        opsOrderSaleOrder.setCreateTime(LocalDateTime.now());
        buildUpdateOrder(order, order2, opsOrderSaleOrder);
    }

    @Override
    protected void buildUpdateOrder(MpOrderResponse.Order order, MpOrderResponse.Order order2, OpsOrderSaleOrder opsOrderSaleOrder) {
        MpOrderResponse.PlatformInfo platformInfo = order.getPlatform_info().get(0);
        MpOrderResponse.BuyerInfo buyersInfo = order.getBuyers_info();
        MpOrderResponse.AddressInfo addressInfo = order.getAddress_info();

        String fulfillmentChannel = null;
        if (Objects.equals(order.getDelivery_type(), 2)) {
            fulfillmentChannel = "MFN";
        } else if (Objects.equals(order.getDelivery_type(), 3) && "Walmart".equalsIgnoreCase(platformInfo.getPlatform_code())) {
            fulfillmentChannel = "WFS";
        } else if (Objects.equals(order.getDelivery_type(), 3) && "Amazon".equalsIgnoreCase(platformInfo.getPlatform_code())) {
            fulfillmentChannel = "AFN";
        }

        opsOrderSaleOrder.setFulfillmentChannel(fulfillmentChannel);
        opsOrderSaleOrder.setOrderStatus(String.valueOf(order.getStatus()));
        opsOrderSaleOrder.setCurrency(order.getAmount_currency());
        opsOrderSaleOrder.setIsAssessed(null);
        opsOrderSaleOrder.setIsMcfOrder(null);
        opsOrderSaleOrder.setIsReturnOrder(null);
        opsOrderSaleOrder.setIsReplacedOrder(null);
        opsOrderSaleOrder.setIsReplacementOrder(null);
        opsOrderSaleOrder.setPurchaseDateLocal(CommonUtils.convertStringToLocalDateTime(platformInfo.getPurchase_time(), CommonUtils.DATETIME_FORMATTER, ZoneId.of("UTC"), null));
        opsOrderSaleOrder.setPurchaseDateLocalUtc(CommonUtils.convertStringToLocalDateTime(platformInfo.getPurchase_time(), CommonUtils.DATETIME_FORMATTER, ZoneId.of("UTC"), null));
        opsOrderSaleOrder.setPurchaseDateLocalBjt(CommonUtils.convertStringToLocalDateTime(platformInfo.getPurchase_time(), CommonUtils.DATETIME_FORMATTER, ZoneId.of("UTC"), ZoneId.of("Asia/Shanghai")));
        opsOrderSaleOrder.setLastUpdateDate(CommonUtils.convertStringToLocalDateTime(order.getUpdate_time(), CommonUtils.DATETIME_FORMATTER, ZoneId.of("UTC"), null));
        opsOrderSaleOrder.setLastUpdateDateUtc(CommonUtils.convertStringToLocalDateTime(order.getUpdate_time(), CommonUtils.DATETIME_FORMATTER, ZoneId.of("UTC"), null));
        opsOrderSaleOrder.setPaymentMethod(null);
        opsOrderSaleOrder.setPostedDate(CommonUtils.convertStringToLocalDateTime(order.getGlobal_payment_time(), CommonUtils.DATETIME_FORMATTER, ZoneId.of("UTC"), null));
        opsOrderSaleOrder.setPostedDateUtc(CommonUtils.convertStringToLocalDateTime(order.getGlobal_payment_time(), CommonUtils.DATETIME_FORMATTER, ZoneId.of("UTC"), null));
        opsOrderSaleOrder.setPostedDateBjt(CommonUtils.convertStringToLocalDateTime(order.getGlobal_payment_time(), CommonUtils.DATETIME_FORMATTER, ZoneId.of("UTC"), ZoneId.of("Asia/Shanghai")));
        opsOrderSaleOrder.setPostedStatus(platformInfo.getPayment_status());
        opsOrderSaleOrder.setShipServiceLevel(CollectionUtils.isNotEmpty(order.getCustomer_shipping_list()) ? order.getCustomer_shipping_list().get(0) : null);
        opsOrderSaleOrder.setOrderType("StandardOrder");
        opsOrderSaleOrder.setLatestShipDate(CommonUtils.convertStringToLocalDateTime(platformInfo.getLatest_ship_time(), CommonUtils.DATETIME_FORMATTER, ZoneId.of("UTC"), null));
        opsOrderSaleOrder.setRemark(order.getRemark());
        opsOrderSaleOrder.setTrackingNo(null);
        opsOrderSaleOrder.setSenderTaxNo(null);

        opsOrderSaleOrder.setName(buyersInfo.getBuyer_name());
        opsOrderSaleOrder.setPhone(buyersInfo.getBuyer_no());
        opsOrderSaleOrder.setEmail(buyersInfo.getBuyer_email());
        opsOrderSaleOrder.setBuyerRemark(buyersInfo.getBuyer_note());
        opsOrderSaleOrder.setRecipient(addressInfo.getReceiver_name());
        opsOrderSaleOrder.setAddressLine1(addressInfo.getAddress_line1());
        opsOrderSaleOrder.setAddressLine2(addressInfo.getAddress_line2());
        opsOrderSaleOrder.setAddressLine3(addressInfo.getAddress_line3());
        opsOrderSaleOrder.setCountry(addressInfo.getReceiver_country_code());
        opsOrderSaleOrder.setCity(addressInfo.getCity());
        opsOrderSaleOrder.setStateProvince(addressInfo.getState_or_region());
        opsOrderSaleOrder.setDistrictCounty(addressInfo.getDistrict());
        opsOrderSaleOrder.setPostalCode(addressInfo.getPostal_code());
        opsOrderSaleOrder.setDoorNo(addressInfo.getDoorplate_no());
        opsOrderSaleOrder.setCompanyName(addressInfo.getCompany_name());

        opsOrderSaleOrder.setIsBusinessOrder(null);
        opsOrderSaleOrder.setIsPrime(null);
        opsOrderSaleOrder.setIsPremiumOrder(null);
        opsOrderSaleOrder.setIsPromotion(null);
        opsOrderSaleOrder.setOrderTotalAmount(null);
        opsOrderSaleOrder.setShippingPriceAmount(null);
        opsOrderSaleOrder.setTaxAmount(null);
        opsOrderSaleOrder.setPredictProfit(null);
        opsOrderSaleOrder.setPredictGrossProfit(null);
        opsOrderSaleOrder.setUpdateBy(OpsUserContext.getTenantId());
        opsOrderSaleOrder.setUpdateTime(LocalDateTime.now());
    }

    @Override
    protected void buildInsertOrderSku(MpOrderResponse.Order order, MpOrderResponse.Order order2, MpOrderResponse.ItemInfo itemInfo, OpsOrderSaleOrder opsOrderSaleOrder, OpsOrderSaleOrderSku opsOrderSaleOrderSku) {
        opsOrderSaleOrderSku.setId(SnowflakeIdWorkerUtils.generateId());
        opsOrderSaleOrderSku.setTenantId(OpsUserContext.getTenantId());
        opsOrderSaleOrderSku.setAsin(itemInfo.getProduct_no());
        opsOrderSaleOrderSku.setMsku(itemInfo.getMsku());
        opsOrderSaleOrderSku.setSku(itemInfo.getLocal_sku());
        opsOrderSaleOrderSku.setPlatform_order_no(itemInfo.getPlatform_order_no());
        opsOrderSaleOrderSku.setCreateBy(OpsUserContext.getTenantId());
        opsOrderSaleOrderSku.setCreateTime(LocalDateTime.now());
        buildUpdateOrderSku(order, order2, itemInfo, opsOrderSaleOrder, opsOrderSaleOrderSku);
    }

    @Override
    protected void buildUpdateOrderSku(MpOrderResponse.Order order, MpOrderResponse.Order order2, MpOrderResponse.ItemInfo itemInfo, OpsOrderSaleOrder opsOrderSaleOrder, OpsOrderSaleOrderSku opsOrderSaleOrderSku) {
        opsOrderSaleOrderSku.setTitle(itemInfo.getTitle());
        opsOrderSaleOrderSku.setProductName(itemInfo.getLocal_product_name());
        opsOrderSaleOrderSku.setSpecificationInfo(itemInfo.getCustomized_url());
        opsOrderSaleOrderSku.setIsCustomized(StringUtils.isNotBlank(itemInfo.getCustomized_url()) ? 1 : 0);
        opsOrderSaleOrderSku.setOrderItemId(itemInfo.getOrder_item_no());
        opsOrderSaleOrderSku.setQuantityOrdered(itemInfo.getQuantity());
        opsOrderSaleOrderSku.setQuantityShipped(null);
        opsOrderSaleOrderSku.setUnitPriceAmount(itemInfo.getUnit_price_amount()); // todo
        opsOrderSaleOrderSku.setTaxAmount(null);
        opsOrderSaleOrderSku.setItemPriceAmount(null);
        opsOrderSaleOrderSku.setItemTaxAmount(null);
        opsOrderSaleOrderSku.setRemark(null);
        opsOrderSaleOrderSku.setShippingPriceAmount(null);
        opsOrderSaleOrderSku.setShippingTaxAmount(null);
        opsOrderSaleOrderSku.setShippingDiscountAmount(null);
        opsOrderSaleOrderSku.setShippingDiscountTaxAmount(null);
        opsOrderSaleOrderSku.setGiftWrapPriceAmount(null);
        opsOrderSaleOrderSku.setGiftWrapTaxAmount(null);
        opsOrderSaleOrderSku.setPromotionAmount(null);
        opsOrderSaleOrderSku.setPromotionDiscountTaxAmount(null);
        opsOrderSaleOrderSku.setCodFeeAmount(null);
        opsOrderSaleOrderSku.setCodFeeDiscountAmount(null);
        opsOrderSaleOrderSku.setFbaShipmentAmount(null);
        opsOrderSaleOrderSku.setCommissionAmount(null);
        opsOrderSaleOrderSku.setPointsMonetaryValueAmount(null);
        opsOrderSaleOrderSku.setOtherAmount(null);
        opsOrderSaleOrderSku.setGrossIncome(null);
        opsOrderSaleOrderSku.setInventoryCost(null);
        opsOrderSaleOrderSku.setCgPrice(null);
        opsOrderSaleOrderSku.setCgTransportCosts(null);
        opsOrderSaleOrderSku.setOtherCosts(null);
        opsOrderSaleOrderSku.setProfit(null);
        opsOrderSaleOrderSku.setProfitRate(null);
        opsOrderSaleOrderSku.setUpdateBy(OpsUserContext.getTenantId());
        opsOrderSaleOrderSku.setUpdateTime(LocalDateTime.now());
    }

}
