package com.dhp.oms.ds.infrastructure.mapper.outboundorder;

import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.dhp.oms.framework.context.tenant.TenentContext;
import com.dhp.oms.framework.core.entity.ops.outboundorder.OutboundOrderSku;
import com.dhp.oms.framework.core.mapper.AbstractBaseMapper;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.ibatis.annotations.Mapper;

import java.util.Collections;
import java.util.List;
import java.util.Set;

/**
 * <p>
 * 销售出库单明细 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-20
 */
@Mapper
public interface OutboundOrderSkuMapper extends AbstractBaseMapper<OutboundOrderSku> {

    default List<OutboundOrderSku> findByWoId(String woId) {
        return new LambdaQueryChainWrapper<>(this)
                .eq(OutboundOrderSku::getTenantId, TenentContext.getTenantId())
                .eq(OutboundOrderSku::getDeleted, false)
                .eq(OutboundOrderSku::getWoId, woId)
                .list();
    }

    default List<OutboundOrderSku> findByWoIds(Set<String> woIds) {
        if (CollectionUtils.isEmpty(woIds)) {
            return Collections.emptyList();
        }
        return new LambdaQueryChainWrapper<>(this)
                .eq(OutboundOrderSku::getTenantId, TenentContext.getTenantId())
                .eq(OutboundOrderSku::getDeleted, false)
                .in(OutboundOrderSku::getWoId, woIds)
                .list();
    }

    default List<OutboundOrderSku> findByWodIds(String woId, Set<String> wodIds) {
        return new LambdaQueryChainWrapper<>(this)
                .eq(OutboundOrderSku::getTenantId, TenentContext.getTenantId())
                .eq(OutboundOrderSku::getDeleted, false)
                .eq(OutboundOrderSku::getWoId, woId)
                .in(CollectionUtils.isNotEmpty(wodIds), OutboundOrderSku::getWodId, wodIds)
                .list();
    }

}
