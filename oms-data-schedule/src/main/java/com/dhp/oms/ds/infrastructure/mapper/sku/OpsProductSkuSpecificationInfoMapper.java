package com.dhp.oms.ds.infrastructure.mapper.sku;

import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import com.dhp.oms.framework.context.user.OpsUserContext;
import com.dhp.oms.framework.core.entity.ops.sku.OpsProductSkuPurchaseItem;
import com.dhp.oms.framework.core.entity.ops.sku.OpsProductSkuSpecificationInfo;
import com.dhp.oms.framework.core.mapper.AbstractBaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface OpsProductSkuSpecificationInfoMapper extends AbstractBaseMapper<OpsProductSkuSpecificationInfo> {

    default Boolean deleteByNotIdList(List<String> notInSkuIdList) {
        return new LambdaUpdateChainWrapper<>(this)
                .notIn(OpsProductSkuSpecificationInfo::getSkuId, notInSkuIdList)
                .eq(OpsProductSkuSpecificationInfo::getTenantId, OpsUserContext.getTenantId())
                .set(OpsProductSkuSpecificationInfo::getDeleted, Boolean.TRUE)
                .update();
    }

}
