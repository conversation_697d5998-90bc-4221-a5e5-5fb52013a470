package com.dhp.oms.ds.infrastructure.mapper.purchase;

import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.dhp.oms.framework.core.entity.ops.purchase.PurchaseRequestSku;
import com.dhp.oms.framework.core.mapper.AbstractBaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <p>
 * 采购-申购单-产品（sku） Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-20
 */
@Mapper
public interface PurchaseRequestSkuMapper extends AbstractBaseMapper<PurchaseRequestSku> {

    default List<PurchaseRequestSku> findByRequestId(Long tenantId, Long requestId) {
        return new LambdaQueryChainWrapper<>(this)
                .eq(PurchaseRequestSku::getTenantId, tenantId)
                .eq(PurchaseRequestSku::getDeleted, Boolean.FALSE)
                .eq(PurchaseRequestSku::getRequestId, requestId)
                .list();
    }

}
