package com.dhp.oms.ds.infrastructure.mapper.afs;

import com.dhp.oms.framework.context.user.OpsUserContext;
import com.dhp.oms.framework.core.entity.ops.afs.OpsOrderAfsRefundOrder;
import com.dhp.oms.framework.core.mapper.AbstractBaseMapper;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.ibatis.annotations.Mapper;

import java.util.Collections;
import java.util.List;

@Mapper
public interface OpsOrderAfsRefundOrderMapper extends AbstractBaseMapper<OpsOrderAfsRefundOrder> {

    default List<OpsOrderAfsRefundOrder> listByOrderNos(List<String> orderNoList) {
        if (CollectionUtils.isEmpty(orderNoList)) {
            return Collections.emptyList();
        }
        return chainQuery()
                .eq(OpsOrderAfsRefundOrder::getTenantId, OpsUserContext.getTenantId())
                .in(OpsOrderAfsRefundOrder::getOmsOrder<PERSON>o, orderNoList)
                .list();
    }

}
