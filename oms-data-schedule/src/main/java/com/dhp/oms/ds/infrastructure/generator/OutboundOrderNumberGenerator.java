package com.dhp.oms.ds.infrastructure.generator;

import com.dhp.oms.framework.exception.BusinessException;
import com.dhp.oms.framework.exception.error.PurchaseError;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import java.text.MessageFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;

@RequiredArgsConstructor
@Component
public class OutboundOrderNumberGenerator {

    private final static String PREFIX = "OB";
    /**
     * 0：tenant_id，如：999999
     * <p>
     * 1：申购单好前缀，如：QC250601
     */
    private final static String REDIS_KEY_FOMART = "outboundorder:number:generator:{0}:{1}";
    private final static String REDISSON_LOCK_FOMART = "outboundorder:number:generator:lock:{0}:{1}";


    private final StringRedisTemplate stringRedisTemplate;
    private final RedissonClient redissonClient;

    public String generate(Long tenantId) {
        return generate(tenantId, 1).stream().findFirst().orElse(null);
    }

    public List<String> generate(Long tenantId, int size) {
        RLock lock = redissonClient.getLock(redissonLockKey(tenantId));
        try {
            boolean tryLock = lock.tryLock(10, 10, TimeUnit.SECONDS);
            if (!tryLock) {
                throw new BusinessException(PurchaseError.GENERATE_NO_FAILED);
            }

            long value = getValue(tenantId);
            try {
                String numberPrefix = generateNumberPrefix();
                List<String> s = new ArrayList<>();
                for (int i = 1; i <= size; i++) {
                    String pad = StringUtils.leftPad(String.valueOf(value + i), 4, "0");
                    String number = numberPrefix + pad;
                    s.add(number);
                }
                return s;
            } finally {
                refreshValue(tenantId, value + size);
            }
        } catch (Exception e) {
            throw new BusinessException(PurchaseError.GENERATE_NO_FAILED);
        } finally {
            if (lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    private void refreshValue(Long tenantId, Long value) {
        stringRedisTemplate.opsForValue().set(redisKey(tenantId), value.toString(), 3, TimeUnit.DAYS);
    }

    private Long getValue(Long tenantId) {
        String key = redisKey(tenantId);
        String val = stringRedisTemplate.opsForValue().get(key);
        if (val == null) {
            return 0L;
        }

        return Long.valueOf(val);
    }

    private String redisKey(Long tenantId) {
        return MessageFormat.format(REDIS_KEY_FOMART, tenantId.toString(), generateNumberPrefix());
    }

    private String redissonLockKey(Long tenantId) {
        return MessageFormat.format(REDISSON_LOCK_FOMART, tenantId.toString(), generateNumberPrefix());
    }

    private static String generateNumberPrefix() {
        return PREFIX + LocalDate.now().format(DateTimeFormatter.ofPattern("yyMMdd"));
    }

    public static void main(String[] args) {
        System.out.println(generateNumberPrefix());
    }

}
