package com.dhp.oms.ds.infrastructure.mapper.spu;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import com.dhp.oms.framework.context.user.OpsUserContext;
import com.dhp.oms.framework.core.entity.ops.spu.ProductSpuBaseInfo;
import com.dhp.oms.framework.core.mapper.AbstractBaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <p>
 * SPU 基础信息 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-05
 */
@Mapper
public interface ProductSpuBaseInfoMapper extends AbstractBaseMapper<ProductSpuBaseInfo> {

    default List<ProductSpuBaseInfo> listByPsIdList(List<Long> psIdList) {
        return selectList(Wrappers.lambdaQuery(ProductSpuBaseInfo.class)
                .select(ProductSpuBaseInfo::getId,
                        ProductSpuBaseInfo::getPsId,
                        ProductSpuBaseInfo::getSpu)
                .eq(ProductSpuBaseInfo::getTenantId, OpsUserContext.getTenantId())
                .in(ProductSpuBaseInfo::getPsId, psIdList));
    }

    default Boolean deleteByNotIdList(List<Long> notInIdList) {
        return new LambdaUpdateChainWrapper<>(this)
                .notIn(ProductSpuBaseInfo::getId, notInIdList)
                .eq(ProductSpuBaseInfo::getTenantId, OpsUserContext.getTenantId())
                .set(ProductSpuBaseInfo::getDeleted, Boolean.TRUE)
                .update();
    }

}
