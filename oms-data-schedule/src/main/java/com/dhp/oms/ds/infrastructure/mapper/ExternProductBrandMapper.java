package com.dhp.oms.ds.infrastructure.mapper;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.dhp.oms.framework.core.entity.extern.ExternProductBrand;
import com.dhp.oms.framework.core.mapper.AbstractBaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <p>
 * 产品品牌表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-21
 */
@Mapper
public interface ExternProductBrandMapper extends AbstractBaseMapper<ExternProductBrand> {

    default Boolean saveOrUpdate(List<ExternProductBrand> list){
        if (ObjectUtil.isEmpty(list)) {
            return true;
        }
        List<Long> bids = list.stream().map(ExternProductBrand::getBid).collect(Collectors.toList());
        List<ExternProductBrand> existList = new LambdaQueryChainWrapper<>(this)
                .in(ExternProductBrand::getBid, bids).eq(ExternProductBrand::getDeleted, false)
                .list();
        // 分离插入和更新数据
        List<ExternProductBrand> toInsert = new ArrayList<>();
        List<ExternProductBrand> toUpdate = new ArrayList<>();
        for (ExternProductBrand item : list) {
            Optional<ExternProductBrand> any = existList.stream()
                    .filter(e -> e.getBid().equals(item.getBid())).findAny();
            if (any.isPresent()) {
                item.setId(any.get().getId());
                toUpdate.add(item);
            } else {
                toInsert.add(item);
            }
        }
        // 执行批量操作
        if (!toInsert.isEmpty()) {
            insertBatch(toInsert, toInsert.size());
        }
        if (!toUpdate.isEmpty()) {
            updateBatchById(toUpdate);
        }
        return true;
    }
}
