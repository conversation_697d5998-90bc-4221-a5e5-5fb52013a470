package com.dhp.oms.ds.enums;

import lombok.Getter;

@Getter
public enum SynTaskCodeEnum {
    syncProductSpu("syncProductSpu", "领星SPU同步"),
    syncProductSku("syncProductSku", "领星SKU同步"),
    syncMwsOrderFull("syncMwsOrderFull", "领星WMS订单同步（全量）"),
    syncMwsOrderIncr("syncMwsOrderIncr", "领星WMS订单同步（增量）"),
    syncFbmOrderFull("syncFbmOrderFull", "领星FBM订单同步（全量）"),
    syncFbmOrderIncr("syncFbmOrderIncr", "领星FBM订单同步（增量）"),
    syncMpOrderFull("syncMpOrderFull", "领星MP订单同步（全量）"),
    syncMpOrderIncr("syncMpOrderIncr", "领星MP订单同步（增量）"),
    ;

    SynTaskCodeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    private String code;
    private String desc;

}
