package com.dhp.oms.ds.infrastructure.mapper.order;

import com.dhp.oms.framework.context.user.OpsUserContext;
import com.dhp.oms.framework.core.entity.ops.order.OpsOrderSaleOrder;
import com.dhp.oms.framework.core.entity.ops.sku.OpsProductSkuBaseInfo;
import com.dhp.oms.framework.core.mapper.AbstractBaseMapper;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.ibatis.annotations.Mapper;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

@Mapper
public interface OpsOrderSaleOrderMapper extends AbstractBaseMapper<OpsOrderSaleOrder> {

    default List<OpsOrderSaleOrder> listByOmsOrderNos(List<String> omsOrderNoList) {
        if (CollectionUtils.isEmpty(omsOrderNoList)) {
            return Collections.emptyList();
        }
        return chainQuery()
                .eq(OpsOrderSaleOrder::getTenantId, OpsUserContext.getTenantId())
                .in(OpsOrderSaleOrder::getOmsOrderNo, omsOrderNoList)
                .list();
    }

}
