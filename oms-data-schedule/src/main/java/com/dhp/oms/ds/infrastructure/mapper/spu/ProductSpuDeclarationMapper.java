package com.dhp.oms.ds.infrastructure.mapper.spu;

import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import com.dhp.oms.framework.context.user.OpsUserContext;
import com.dhp.oms.framework.core.entity.ops.sku.OpsProductSkuDeclaration;
import com.dhp.oms.framework.core.entity.ops.spu.ProductSpuDeclaration;
import com.dhp.oms.framework.core.mapper.AbstractBaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <p>
 * SPU申报信息 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-05
 */
@Mapper
public interface ProductSpuDeclarationMapper extends AbstractBaseMapper<ProductSpuDeclaration> {

    default Boolean deleteByNotIdList(List<Long> notInPsIdList) {
        return new LambdaUpdateChainWrapper<>(this)
                .notIn(ProductSpuDeclaration::getSpuId, notInPsIdList)
                .eq(ProductSpuDeclaration::getTenantId, OpsUserContext.getTenantId())
                .set(ProductSpuDeclaration::getDeleted, Boolean.TRUE)
                .update();
    }

}
