package com.dhp.oms.ds.infrastructure.mapper.email;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.dhp.oms.framework.core.entity.ops.email.OpsErpEmailAnalyzeInfo;
import com.dhp.oms.framework.core.mapper.AbstractBaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Set;

/**
 * <p>
 * 邮件解析 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-20
 */
@Mapper
public interface OpsErpEmailAnalyzeInfoMapper extends AbstractBaseMapper<OpsErpEmailAnalyzeInfo> {

    default List<OpsErpEmailAnalyzeInfo> selectByWebmailUuids(Set<String> uuidList){
        return selectList(new QueryWrapper<OpsErpEmailAnalyzeInfo>()
                .in("webmail_uuid", uuidList));
    }
}
