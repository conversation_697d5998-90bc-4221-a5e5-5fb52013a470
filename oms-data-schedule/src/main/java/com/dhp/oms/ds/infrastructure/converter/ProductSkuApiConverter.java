package com.dhp.oms.ds.infrastructure.converter;

import com.alibaba.fastjson.JSON;
import com.dhp.oms.framework.context.user.OpsUserContext;
import com.dhp.oms.framework.core.entity.ops.product.OpsProductCategory;
import com.dhp.oms.framework.core.entity.ops.sku.OpsProductSkuBaseInfo;
import com.dhp.oms.framework.core.entity.ops.sku.OpsProductSkuClearanceFee;
import com.dhp.oms.framework.core.entity.ops.sku.OpsProductSkuDeclaration;
import com.dhp.oms.framework.core.entity.ops.sku.OpsProductSkuPurchaseInfo;
import com.dhp.oms.framework.core.entity.ops.sku.OpsProductSkuPurchaseItem;
import com.dhp.oms.framework.core.entity.ops.sku.OpsProductSkuSpecificationInfo;
import com.dhp.oms.framework.core.entity.ops.sku.OpsProductSkuSpecificationSingle;
import com.dhp.oms.framework.core.enums.ops.product.ProductSkuType;
import com.dhp.oms.framework.utils.SnowflakeIdWorkerUtils;
import com.dhp.oms.third.api.module.sku.response.ProductSkuDetailResponse;
import com.dhp.oms.third.api.module.sku.response.ProductSkuResponse;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

@Mapper
public interface ProductSkuApiConverter {

    ProductSkuApiConverter INSTANCE = Mappers.getMapper(ProductSkuApiConverter.class);

    default void buildOpsProductSkuBaseInfoInsert(ProductSkuResponse skuEntry,
                                                  ProductSkuDetailResponse skuDetailEntry,
                                                  Map<Long, OpsProductCategory> opsProductCategoryMap,
                                                  OpsProductSkuBaseInfo baseInfo,
                                                  List<OpsProductSkuBaseInfo> opsProductSkuBaseInfoList) {

        baseInfo.setId(SnowflakeIdWorkerUtils.generateId());
        baseInfo.setTenantId(OpsUserContext.getTenantId());
        baseInfo.setSkuId(String.valueOf(skuEntry.getId()));
        baseInfo.setSku(skuEntry.getSku());
        baseInfo.setCreateTime(LocalDateTime.now());
        baseInfo.setCreateBy(OpsUserContext.getTenantId());
        buildOpsProductSkuBaseInfoUpdate(skuEntry, skuDetailEntry, opsProductCategoryMap, baseInfo, opsProductSkuBaseInfoList);
    }

    default void buildOpsProductSkuBaseInfoUpdate(ProductSkuResponse skuEntry,
                                                  ProductSkuDetailResponse skuDetailEntry,
                                                  Map<Long, OpsProductCategory> opsProductCategoryMap,
                                                  OpsProductSkuBaseInfo baseInfo,
                                                  List<OpsProductSkuBaseInfo> opsProductSkuBaseInfoList) {
        List<String> pictureList = Optional.ofNullable(skuDetailEntry.getPictureList())
                .orElse(Collections.emptyList())
                .stream()
                .sorted(Comparator.nullsFirst(Comparator.comparing(ProductSkuDetailResponse.Picture::getIsPrimary)).reversed())
                .filter(Objects::nonNull)
                .map(ProductSkuDetailResponse.Picture::getPicUrl)
                .collect(Collectors.toList());
        baseInfo.setImage(JSON.toJSONString(pictureList));
        baseInfo.setSkuDescription(skuDetailEntry.getDescription());
        baseInfo.setSpuId(String.valueOf(skuEntry.getPs_id()));
        baseInfo.setSkuEngDescription(null);
        baseInfo.setBid(skuEntry.getBid());
        baseInfo.setBrandName(skuEntry.getBrand_name());
        baseInfo.setProductAbbreviation(skuEntry.getProduct_name());
        baseInfo.setCid(skuEntry.getCid());
        baseInfo.setSkuCategory(Optional.ofNullable(opsProductCategoryMap.get(skuEntry.getCid())).map(OpsProductCategory::getCategoryCode).orElse(null));
        baseInfo.setSkuType(ProductSkuType.SINGLE.getCode());
        baseInfo.setSkuUnit(skuDetailEntry.getUnit());
        baseInfo.setSkuEan(null);
        baseInfo.setSkuUpc(null);
        baseInfo.setSkuStatus(String.valueOf(skuDetailEntry.getStatus()));
        baseInfo.setDevManagerId(skuDetailEntry.getProductDeveloperUid());
        baseInfo.setDevManager(skuDetailEntry.getProductDeveloper());
        baseInfo.setUpdateTime(LocalDateTime.now());
        baseInfo.setUpdateBy(OpsUserContext.getTenantId());
        opsProductSkuBaseInfoList.add(baseInfo);
    }

    default void buildOpsProductSkuDeclarationInsert(ProductSkuResponse skuEntry,
                                                     ProductSkuDetailResponse.Declaration declarationEntry,
                                                     List<OpsProductSkuDeclaration> opsProductSkuDeclarationList) {
        OpsProductSkuDeclaration declaration = new OpsProductSkuDeclaration();
        declaration.setId(SnowflakeIdWorkerUtils.generateId());
        declaration.setTenantId(OpsUserContext.getTenantId());
        declaration.setSkuId(String.valueOf(skuEntry.getId()));
        declaration.setCreateTime(LocalDateTime.now());
        declaration.setCreateBy(OpsUserContext.getTenantId());
        buildOpsProductSkuDeclarationUpdate(skuEntry, declarationEntry, declaration, opsProductSkuDeclarationList);
    }

    default void buildOpsProductSkuDeclarationUpdate(ProductSkuResponse skuEntry,
                                                     ProductSkuDetailResponse.Declaration declarationEntry,
                                                     OpsProductSkuDeclaration declaration,
                                                     List<OpsProductSkuDeclaration> opsProductSkuDeclarationList) {
        if (Objects.isNull(declarationEntry)) {
            return;
        }
        declaration.setOriginCountry(declarationEntry.getCustomsDeclarationOriginProduce());
        declaration.setDeclarationUnit(declarationEntry.getCustomsDeclarationUnit());
        declaration.setDeclarationModel(declarationEntry.getCustomsDeclarationSpec());
        declaration.setDeclarationElement(declarationEntry.getOtherDeclareElement());
        declaration.setUpdateTime(LocalDateTime.now());
        declaration.setUpdateBy(OpsUserContext.getTenantId());
        opsProductSkuDeclarationList.add(declaration);
    }

    default void buildOpsProductSkuClearanceFeeInsert(ProductSkuResponse skuEntry,
                                                      ProductSkuDetailResponse.Clearance clearanceEntry,
                                                      List<OpsProductSkuClearanceFee> opsProductSkuClearanceFeeList) {
        OpsProductSkuClearanceFee clearanceFee = new OpsProductSkuClearanceFee();
        clearanceFee.setId(SnowflakeIdWorkerUtils.generateId());
        clearanceFee.setTenantId(OpsUserContext.getTenantId());
        clearanceFee.setSkuId(String.valueOf(skuEntry.getId()));
        clearanceFee.setCreateTime(LocalDateTime.now());
        clearanceFee.setCreateBy(OpsUserContext.getTenantId());
        buildOpsProductSkuClearanceFeeUpdate(skuEntry, clearanceEntry, clearanceFee, opsProductSkuClearanceFeeList);
    }

    default void buildOpsProductSkuClearanceFeeUpdate(ProductSkuResponse skuEntry,
                                                      ProductSkuDetailResponse.Clearance clearanceEntry,
                                                      OpsProductSkuClearanceFee clearanceFee,
                                                      List<OpsProductSkuClearanceFee> opsProductSkuClearanceFeeList) {
        if (Objects.isNull(clearanceEntry)) {
            return;
        }
        clearanceFee.setCountryCode(null);
        clearanceFee.setDefaultFreight(null);
        clearanceFee.setDefaultFreightUnit(null);
        clearanceFee.setHsCode(clearanceEntry.getCustomsClearanceHsCode());
        clearanceFee.setUnitPrice(Optional.ofNullable(clearanceEntry.getCustomsClearancePrice()).map(BigDecimal::new).map(BigDecimal::intValue).orElse(null));
        clearanceFee.setUnitPriceUnit(clearanceEntry.getCustomsClearancePriceCurrency());
        clearanceFee.setTaxRate(Optional.ofNullable(clearanceEntry.getCustomsClearanceTaxRate()).map(Double::parseDouble).orElse(null));
        clearanceFee.setRemark(clearanceEntry.getCustomsClearanceRemark());
        clearanceFee.setUpdateTime(LocalDateTime.now());
        clearanceFee.setUpdateBy(OpsUserContext.getTenantId());
        opsProductSkuClearanceFeeList.add(clearanceFee);
    }

    default void buildOpsProductSkuPurchaseInfoInsert(ProductSkuResponse skuEntry,
                                                      ProductSkuDetailResponse detailResponse,
                                                      List<OpsProductSkuPurchaseInfo> opsProductSkuPurchaseInfoList) {
        OpsProductSkuPurchaseInfo purchaseInfo = new OpsProductSkuPurchaseInfo();
        purchaseInfo.setId(SnowflakeIdWorkerUtils.generateId());
        purchaseInfo.setTenantId(OpsUserContext.getTenantId());
        purchaseInfo.setSkuId(String.valueOf(skuEntry.getId()));
        purchaseInfo.setCreateTime(LocalDateTime.now());
        purchaseInfo.setCreateBy(OpsUserContext.getTenantId());
        buildOpsProductSkuPurchaseInfoUpdate(skuEntry, detailResponse, purchaseInfo, opsProductSkuPurchaseInfoList);
    }

    default void buildOpsProductSkuPurchaseInfoUpdate(ProductSkuResponse skuEntry,
                                                      ProductSkuDetailResponse detailResponse,
                                                      OpsProductSkuPurchaseInfo purchaseInfo,
                                                      List<OpsProductSkuPurchaseInfo> opsProductSkuPurchaseInfoList) {
        purchaseInfo.setPurchaserId(detailResponse.getCgOptUsername());
        purchaseInfo.setLeadTime(Long.valueOf(detailResponse.getCgDelivery()));
        purchaseInfo.setUpdateTime(LocalDateTime.now());
        purchaseInfo.setUpdateBy(OpsUserContext.getTenantId());
        opsProductSkuPurchaseInfoList.add(purchaseInfo);
    }

    default void buildOpsProductSkuPurchaseItemInsert(ProductSkuResponse skuEntry,
                                                      ProductSkuDetailResponse detailResponse,
                                                      List<OpsProductSkuPurchaseItem> opsProductSkuPurchaseItemList) {
        OpsProductSkuPurchaseItem purchaseItem = new OpsProductSkuPurchaseItem();
        purchaseItem.setId(SnowflakeIdWorkerUtils.generateId());
        purchaseItem.setTenantId(OpsUserContext.getTenantId());
        purchaseItem.setSkuId(String.valueOf(skuEntry.getId()));
        purchaseItem.setCreateTime(LocalDateTime.now());
        purchaseItem.setCreateBy(OpsUserContext.getTenantId());
        buildOpsProductSkuPurchaseItemUpdate(skuEntry, detailResponse, purchaseItem, opsProductSkuPurchaseItemList);
    }

    default void buildOpsProductSkuPurchaseItemUpdate(ProductSkuResponse skuEntry,
                                                      ProductSkuDetailResponse detailResponse,
                                                      OpsProductSkuPurchaseItem purchaseItem,
                                                      List<OpsProductSkuPurchaseItem> opsProductSkuPurchaseItemList) {
        purchaseItem.setUnitPrice(String.valueOf(detailResponse.getCgPrice()));
        purchaseItem.setUnitPrice(String.valueOf(detailResponse.getCgPrice()));
        purchaseItem.setUpdateTime(LocalDateTime.now());
        purchaseItem.setUpdateBy(OpsUserContext.getTenantId());
        opsProductSkuPurchaseItemList.add(purchaseItem);
    }

    default void buildOpsProductSkuSpecificationInfoInsert(ProductSkuResponse skuEntry,
                                                           ProductSkuDetailResponse detailResponse,
                                                           List<OpsProductSkuSpecificationInfo> opsProductSkuSpecificationInfoList) {
        OpsProductSkuSpecificationInfo specificationInfo = new OpsProductSkuSpecificationInfo();
        specificationInfo.setId(SnowflakeIdWorkerUtils.generateId());
        specificationInfo.setTenantId(OpsUserContext.getTenantId());
        specificationInfo.setSkuId(String.valueOf(skuEntry.getId()));
        specificationInfo.setCreateTime(LocalDateTime.now());
        specificationInfo.setCreateBy(OpsUserContext.getTenantId());
        buildOpsProductSkuSpecificationInfoUpdate(skuEntry, detailResponse, specificationInfo, opsProductSkuSpecificationInfoList);
    }

    default void buildOpsProductSkuSpecificationInfoUpdate(ProductSkuResponse skuEntry,
                                                           ProductSkuDetailResponse detailResponse,
                                                           OpsProductSkuSpecificationInfo specificationInfo,
                                                           List<OpsProductSkuSpecificationInfo> opsProductSkuSpecificationInfoList) {
        String supplierId = Optional.ofNullable(detailResponse.getSupplierQuote())
                .orElse(Collections.emptyList())
                .stream()
                .filter(item -> Objects.equals(item.getIsPrimary(), 1))
                .findFirst()
                .map(ProductSkuDetailResponse.SupplierQuote::getSupplierId)
                .orElse(null);
        specificationInfo.setSkuSupplierId(supplierId);
        specificationInfo.setSkuQuantityPerBox(detailResponse.getCgBoxPcs());
        specificationInfo.setSkuOuterBoxLength(Optional.ofNullable(detailResponse.getCgBoxLength()).map(BigDecimal::toPlainString).orElse(null));
        specificationInfo.setSkuOuterBoxWide(Optional.ofNullable(detailResponse.getCgBoxWidth()).map(BigDecimal::toPlainString).orElse(null));
        specificationInfo.setSkuOuterBoxHigh(Optional.ofNullable(detailResponse.getCgBoxHeight()).map(BigDecimal::toPlainString).orElse(null));
        specificationInfo.setSkuPackageBoxLength(Optional.ofNullable(detailResponse.getCgPackageLength()).map(BigDecimal::toPlainString).orElse(null));
        specificationInfo.setSkuPackageBoxWide(Optional.ofNullable(detailResponse.getCgPackageWidth()).map(BigDecimal::toPlainString).orElse(null));
        specificationInfo.setSkuPackageBoxHigh(Optional.ofNullable(detailResponse.getCgPackageHeight()).map(BigDecimal::toPlainString).orElse(null));
        specificationInfo.setSkuSingleBoxWeight(Optional.ofNullable(detailResponse.getCgBoxWeight()).map(BigDecimal::toPlainString).orElse(null));
//        specificationInfo.setSkuSingleItemGrossWeight(Optional.ofNullable(detailResponse.getCgProductGrossWeight()).map(BigDecimal::toPlainString).orElse(null));
//        specificationInfo.setSkuSingleItemWeight(Optional.ofNullable(detailResponse.getCgProductNetWeight()).map(BigDecimal::toPlainString).orElse(null));
        specificationInfo.setUpdateTime(LocalDateTime.now());
        specificationInfo.setUpdateBy(OpsUserContext.getTenantId());
        opsProductSkuSpecificationInfoList.add(specificationInfo);
    }

    default void buildOpsProductSkuSpecificationSingleInsert(ProductSkuResponse skuEntry,
                                                             ProductSkuDetailResponse detailResponse,
                                                             List<OpsProductSkuSpecificationSingle> opsProductSkuSpecificationSingleList) {
        OpsProductSkuSpecificationSingle specificationSingle = new OpsProductSkuSpecificationSingle();
        specificationSingle.setId(SnowflakeIdWorkerUtils.generateId());
        specificationSingle.setTenantId(OpsUserContext.getTenantId());
        specificationSingle.setSkuId(String.valueOf(skuEntry.getId()));
        specificationSingle.setCreateTime(LocalDateTime.now());
        specificationSingle.setCreateBy(OpsUserContext.getTenantId());
        buildOpsProductSkuSpecificationSingleUpdate(skuEntry, detailResponse, specificationSingle, opsProductSkuSpecificationSingleList);
    }

    default void buildOpsProductSkuSpecificationSingleUpdate(ProductSkuResponse skuEntry,
                                                             ProductSkuDetailResponse detailResponse,
                                                             OpsProductSkuSpecificationSingle specificationSingle,
                                                             List<OpsProductSkuSpecificationSingle> opsProductSkuSpecificationSingleList) {
        specificationSingle.setSingleItemLength(Optional.ofNullable(detailResponse.getCgProductLength()).map(BigDecimal::toPlainString).orElse(null));
        specificationSingle.setSingleItemWide(Optional.ofNullable(detailResponse.getCgProductWidth()).map(BigDecimal::toPlainString).orElse(null));
        specificationSingle.setSingleItemHigh(Optional.ofNullable(detailResponse.getCgProductHeight()).map(BigDecimal::toPlainString).orElse(null));
        specificationSingle.setSingleItemWeight(Optional.ofNullable(detailResponse.getCgProductNetWeight()).map(BigDecimal::toPlainString).orElse(null));
        specificationSingle.setSingleItemGrossWeight(Optional.ofNullable(detailResponse.getCgProductGrossWeight()).map(BigDecimal::toPlainString).orElse(null));
        specificationSingle.setUpdateTime(LocalDateTime.now());
        specificationSingle.setUpdateBy(OpsUserContext.getTenantId());
        opsProductSkuSpecificationSingleList.add(specificationSingle);
    }

}
