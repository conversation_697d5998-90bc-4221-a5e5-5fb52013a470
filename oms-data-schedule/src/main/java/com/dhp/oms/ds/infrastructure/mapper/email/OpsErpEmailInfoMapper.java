package com.dhp.oms.ds.infrastructure.mapper.email;

import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.dhp.oms.framework.core.entity.ops.email.OpsErpEmailInfo;
import com.dhp.oms.framework.core.mapper.AbstractBaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-19
 */
@Mapper
public interface OpsErpEmailInfoMapper extends AbstractBaseMapper<OpsErpEmailInfo> {

   default List<OpsErpEmailInfo> selectByWebmailUuids(List<String> list){
       return new LambdaQueryChainWrapper<>(this)
               .in(OpsErpEmailInfo::getWebmailUuid,list)
               .list();
   }

   default List<OpsErpEmailInfo> searchReciveEmailList(){
       return new LambdaQueryChainWrapper<>(this)
               .eq(OpsErpEmailInfo::getFlag,"receive")
               .list();
   }

    default  OpsErpEmailInfo searchOneByWebmailUuid(String emailUuid){
       return new LambdaQueryChainWrapper<>(this)
                .eq(OpsErpEmailInfo::getWebmailUuid,emailUuid)
                .one();
    }

}
