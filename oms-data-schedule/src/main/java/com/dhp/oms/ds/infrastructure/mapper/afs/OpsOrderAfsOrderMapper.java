package com.dhp.oms.ds.infrastructure.mapper.afs;

import com.dhp.oms.framework.context.user.OpsUserContext;
import com.dhp.oms.framework.core.entity.ops.afs.OpsOrderAfsOrder;
import com.dhp.oms.framework.core.mapper.AbstractBaseMapper;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.ibatis.annotations.Mapper;

import java.util.Collections;
import java.util.List;

@Mapper
public interface OpsOrderAfsOrderMapper extends AbstractBaseMapper<OpsOrderAfsOrder> {

    default List<OpsOrderAfsOrder> listByOmsOrderNos(List<String> omsOrderNoList) {
        if (CollectionUtils.isEmpty(omsOrderNoList)) {
            return Collections.emptyList();
        }
        return chainQuery()
                .eq(OpsOrderAfsOrder::getTenantId, OpsUserContext.getTenantId())
                .in(OpsOrderAfsOrder::getOrde<PERSON><PERSON>o, omsOrderNoList)
                .list();
    }

}
