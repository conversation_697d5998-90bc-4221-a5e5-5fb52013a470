package com.dhp.oms.ds.infrastructure.mapper.sku;

import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import com.dhp.oms.framework.context.user.OpsUserContext;
import com.dhp.oms.framework.core.entity.ops.sku.OpsProductSkuBaseInfo;
import com.dhp.oms.framework.core.entity.ops.sku.OpsProductSkuClearanceFee;
import com.dhp.oms.framework.core.mapper.AbstractBaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface OpsProductSkuClearanceFeeMapper extends AbstractBaseMapper<OpsProductSkuClearanceFee> {

    default Boolean deleteByNotIdList(List<String> notInSkuIdList) {
        return new LambdaUpdateChainWrapper<>(this)
                .notIn(OpsProductSkuClearanceFee::getSkuId, notInSkuIdList)
                .eq(OpsProductSkuClearanceFee::getTenantId, OpsUserContext.getTenantId())
                .set(OpsProductSkuClearanceFee::getDeleted, Boolean.TRUE)
                .update();
    }

}
