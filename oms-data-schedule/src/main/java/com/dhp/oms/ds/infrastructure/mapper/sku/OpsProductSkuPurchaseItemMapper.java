package com.dhp.oms.ds.infrastructure.mapper.sku;

import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import com.dhp.oms.framework.context.user.OpsUserContext;
import com.dhp.oms.framework.core.entity.ops.sku.OpsProductSkuPurchaseInfo;
import com.dhp.oms.framework.core.entity.ops.sku.OpsProductSkuPurchaseItem;
import com.dhp.oms.framework.core.mapper.AbstractBaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface OpsProductSkuPurchaseItemMapper extends AbstractBaseMapper<OpsProductSkuPurchaseItem> {

    default Boolean deleteByNotIdList(List<String> notInSkuIdList) {
        return new LambdaUpdateChainWrapper<>(this)
                .notIn(OpsProductSkuPurchaseItem::getSkuId, notInSkuIdList)
                .eq(OpsProductSkuPurchaseItem::getTenantId, OpsUserContext.getTenantId())
                .set(OpsProductSkuPurchaseItem::getDeleted, Boolean.TRUE)
                .update();
    }

}
