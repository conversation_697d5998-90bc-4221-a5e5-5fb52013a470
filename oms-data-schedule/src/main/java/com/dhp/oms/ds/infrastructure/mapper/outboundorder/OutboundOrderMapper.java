package com.dhp.oms.ds.infrastructure.mapper.outboundorder;

import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.dhp.oms.framework.context.tenant.TenentContext;
import com.dhp.oms.framework.core.entity.ops.outboundorder.OutboundOrder;
import com.dhp.oms.framework.core.enums.ops.outboundorder.OutboundOrderTypeEnum;
import com.dhp.oms.framework.core.mapper.AbstractBaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Set;

/**
 * <p>
 * 销售出库单 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-20
 */
@Mapper
public interface OutboundOrderMapper extends AbstractBaseMapper<OutboundOrder> {

    default List<OutboundOrder> findSelfShippingByWoIds(Set<String> woIds) {
        return new LambdaQueryChainWrapper<>(this)
                .eq(OutboundOrder::getTenantId, TenentContext.getTenantId())
                .eq(OutboundOrder::getDeleted, false)
                .eq(OutboundOrder::getType, OutboundOrderTypeEnum.SELF_SHIPPING.name())
                .in(OutboundOrder::getWoId, woIds)
                .list();
    }

    default List<OutboundOrder> findAmazonFbaByWoIds(Set<String> woIds) {
        return new LambdaQueryChainWrapper<>(this)
                .eq(OutboundOrder::getTenantId, TenentContext.getTenantId())
                .eq(OutboundOrder::getDeleted, false)
                .eq(OutboundOrder::getType, OutboundOrderTypeEnum.AMAZON_FBA.name())
                .in(OutboundOrder::getWoId, woIds)
                .list();
    }

}
