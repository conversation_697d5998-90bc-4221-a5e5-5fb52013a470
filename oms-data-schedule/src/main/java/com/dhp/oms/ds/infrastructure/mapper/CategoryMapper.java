package com.dhp.oms.ds.infrastructure.mapper;

import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.dhp.oms.framework.context.user.OpsUserContext;
import com.dhp.oms.framework.core.entity.ops.product.OpsProductCategory;
import com.dhp.oms.framework.core.mapper.AbstractBaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;


@Mapper
public interface CategoryMapper extends AbstractBaseMapper<OpsProductCategory> {

    default List<OpsProductCategory> listByCidList(List<Long> cidList) {
        return new LambdaQueryChainWrapper<>(this)
                .in(OpsProductCategory::getCid, cidList)
                .eq(OpsProductCategory::getTenantId, OpsUserContext.getTenantId())
                .list();
    }

}
