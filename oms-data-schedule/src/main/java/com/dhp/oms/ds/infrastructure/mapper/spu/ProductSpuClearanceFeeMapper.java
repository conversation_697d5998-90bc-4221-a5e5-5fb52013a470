package com.dhp.oms.ds.infrastructure.mapper.spu;

import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import com.dhp.oms.framework.context.user.OpsUserContext;
import com.dhp.oms.framework.core.entity.ops.spu.ProductSpuClearanceFee;
import com.dhp.oms.framework.core.entity.ops.spu.ProductSpuDeclaration;
import com.dhp.oms.framework.core.mapper.AbstractBaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <p>
 * SPU清关费用表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-05
 */
@Mapper
public interface ProductSpuClearanceFeeMapper extends AbstractBaseMapper<ProductSpuClearanceFee> {

    default Boolean deleteByNotIdList(List<Long> notInPsIdList) {
        return new LambdaUpdateChainWrapper<>(this)
                .notIn(ProductSpuClearanceFee::getSpuId, notInPsIdList)
                .eq(ProductSpuClearanceFee::getTenantId, OpsUserContext.getTenantId())
                .set(ProductSpuClearanceFee::getDeleted, Boolean.TRUE)
                .update();
    }

}
