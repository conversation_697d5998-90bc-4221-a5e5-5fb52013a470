package com.dhp.oms.ds.infrastructure.service.impl;

import com.dhp.oms.ds.infrastructure.service.AbstractSaleOrderSyncService;
import com.dhp.oms.framework.context.user.OpsUserContext;
import com.dhp.oms.framework.core.entity.ops.order.OpsOrderSaleOrder;
import com.dhp.oms.framework.core.entity.ops.order.OpsOrderSaleOrderSku;
import com.dhp.oms.framework.core.entity.ops.store.StoreInfo;
import com.dhp.oms.framework.utils.CommonUtils;
import com.dhp.oms.framework.utils.SnowflakeIdWorkerUtils;
import com.dhp.oms.third.api.common.ExternApiResult;
import com.dhp.oms.third.api.module.order.MwsOrderApi;
import com.dhp.oms.third.api.module.order.request.MwsOrderDetailRequest;
import com.dhp.oms.third.api.module.order.request.MwsOrderRequest;
import com.dhp.oms.third.api.module.order.response.MwsOrderDetailResponse;
import com.dhp.oms.third.api.module.order.response.MwsOrderResponse;
import com.google.common.collect.Lists;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.BiConsumer;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service("mwsOrderSyncServiceImpl")
public class MwsOrderSyncServiceImpl extends AbstractSaleOrderSyncService<MwsOrderRequest, MwsOrderResponse, MwsOrderDetailResponse, MwsOrderDetailResponse.Item> {

    @Resource
    private MwsOrderApi mwsOrderApi;

    @Override
    public List<List<String>> partitionSidList(List<StoreInfo> storeInfoList) {
        List<String> sid = storeInfoList.stream()
                .map(StoreInfo::getSid)
                .distinct()
                .toList();
        return Lists.partition(sid, 20);
    }

    @Override
    protected MwsOrderRequest newOrderListRequestInstance(List<String> partitionSidList) {
        MwsOrderRequest mwsOrderRequest = new MwsOrderRequest();
        mwsOrderRequest.setSid_list(partitionSidList);
        return mwsOrderRequest;
    }

    @Override
    protected boolean firstPage(MwsOrderRequest mwsOrderRequest) {
        return mwsOrderRequest.getOffset() == 0;
    }

    @Override
    protected boolean hasNextPage(MwsOrderRequest mwsOrderRequest, Long total) {
        return mwsOrderRequest.getOffset() < total;
    }

    @Override
    protected void setNextPage(MwsOrderRequest mwsOrderRequest) {
        mwsOrderRequest.setOffset(mwsOrderRequest.getOffset() + mwsOrderRequest.getLength());
    }

    @Override
    protected List<MwsOrderResponse> getOrderList(ExternApiResult<?> result) {
        return Optional.ofNullable(result)
                .map(ExternApiResult::getData)
                .map(item -> (List<MwsOrderResponse>) item)
                .orElse(Collections.emptyList());
    }

    @Override
    protected Function<MwsOrderResponse, String> obtainGetOrderIdFunc() {
        return MwsOrderResponse::getAmazon_order_id;
    }

    @Override
    protected BiConsumer<MwsOrderResponse, Long> obtainSetBizOrderIdFunc() {
        return MwsOrderResponse::setBizOrderId;
    }

    @Override
    protected Function<MwsOrderResponse, Long> obtainGetBizOrderIdFunc() {
        return MwsOrderResponse::getBizOrderId;
    }

    @Override
    protected Function<MwsOrderDetailResponse.Item, String> obtainGetOrderItemIdFunc() {
        return MwsOrderDetailResponse.Item::getOrder_item_id;
    }

    @Override
    protected BiConsumer<MwsOrderDetailResponse.Item, Long> obtainSetBizOrderItemIdFunc() {
        return MwsOrderDetailResponse.Item::setBizOrderItemId;
    }

    @Override
    protected Function<MwsOrderDetailResponse.Item, Long> obtainGetBizOrderItemIdFunc() {
        return MwsOrderDetailResponse.Item::getBizOrderItemId;
    }

    @Override
    protected List<MwsOrderDetailResponse.Item> obtainOrderSkuList(MwsOrderResponse mwsOrderResponse, MwsOrderDetailResponse mwsOrderDetailResponse) {
        return mwsOrderDetailResponse.getItem_list();
    }

    @Override
    protected ExternApiResult<?> getOrderResponse(MwsOrderRequest mwsOrderRequest, LocalDateTime beginTime, LocalDateTime endTime) {
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        mwsOrderRequest.setDate_type(3);
        mwsOrderRequest.setStart_date(dateTimeFormatter.format(beginTime));
        mwsOrderRequest.setEnd_date(dateTimeFormatter.format(endTime));
        return mwsOrderApi.orders(mwsOrderRequest);
    }

    @Override
    protected Map<String, MwsOrderDetailResponse> getOrderDetailResponse(List<MwsOrderResponse> orderList) {
        return Lists.partition(orderList, 200)
                .stream()
                .flatMap(partitionOrderList -> {
                    MwsOrderDetailRequest request = new MwsOrderDetailRequest();
                    String orderIdList = partitionOrderList.stream().map(MwsOrderResponse::getAmazon_order_id).distinct().collect(Collectors.joining(","));
                    request.setOrder_id(orderIdList);
                    return Optional.ofNullable(mwsOrderApi.orderDetail(request))
                            .map(ExternApiResult::getData)
                            .orElse(Collections.emptyList())
                            .stream();
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toMap(MwsOrderDetailResponse::getAmazon_order_id, Function.identity(), (pre, next) -> pre));
    }

    @Override
    protected void buildInsertOrder(MwsOrderRequest orderListRequest, MwsOrderResponse orderResponse, MwsOrderDetailResponse orderDetailResponse, OpsOrderSaleOrder opsOrderSaleOrder) {
        opsOrderSaleOrder.setId(SnowflakeIdWorkerUtils.generateId());
        opsOrderSaleOrder.setTenantId(OpsUserContext.getTenantId());
        opsOrderSaleOrder.setSid(orderDetailResponse.getSid());
        opsOrderSaleOrder.setOrigin("MWS");
        opsOrderSaleOrder.setSalePlatform("Amazon");
        opsOrderSaleOrder.setPlatformOrderNo(orderDetailResponse.getAmazon_order_id());
        opsOrderSaleOrder.setReferenceNo(null);
        opsOrderSaleOrder.setSalesChannel(orderDetailResponse.getSales_channel());
        opsOrderSaleOrder.setPurchaseOrderNumber(null);
        opsOrderSaleOrder.setOmsOrderNo(null);
        opsOrderSaleOrder.setCreateBy(OpsUserContext.getTenantId());
        opsOrderSaleOrder.setCreateTime(LocalDateTime.now());
        buildUpdateOrder(orderResponse, orderDetailResponse, opsOrderSaleOrder);
    }

    @Override
    protected void buildUpdateOrder(MwsOrderResponse orderResponse, MwsOrderDetailResponse orderDetailResponse, OpsOrderSaleOrder opsOrderSaleOrder) {
        opsOrderSaleOrder.setFulfillmentChannel(orderDetailResponse.getFulfillment_channel());
        opsOrderSaleOrder.setOrderStatus(orderDetailResponse.getOrder_status());
        opsOrderSaleOrder.setCurrency(orderDetailResponse.getOrder_total_currency_code());
        opsOrderSaleOrder.setIsAssessed(Objects.equals(orderDetailResponse.getIs_assessed(), 1));
        opsOrderSaleOrder.setIsMcfOrder(Objects.equals(orderDetailResponse.getIs_mcf_order(), 1));
        opsOrderSaleOrder.setIsReturnOrder(Objects.equals(orderDetailResponse.getIs_return_order(), 1));
        opsOrderSaleOrder.setIsReplacedOrder(Objects.equals(orderDetailResponse.getIs_replaced_order(), 1));
        opsOrderSaleOrder.setIsReplacementOrder(Objects.equals(orderDetailResponse.getIs_replacement_order(), 1));
        opsOrderSaleOrder.setPurchaseDateLocal(CommonUtils.convertStringToLocalDateTime(orderDetailResponse.getPurchase_date_local(), CommonUtils.DATETIME_FORMATTER, ZoneId.of("UTC"), null)); // todo
        opsOrderSaleOrder.setPurchaseDateLocalUtc(CommonUtils.convertStringToLocalDateTime(orderDetailResponse.getPurchase_date_local(), CommonUtils.DATETIME_FORMATTER, ZoneId.of("UTC"), null));
        opsOrderSaleOrder.setPurchaseDateLocalBjt(CommonUtils.convertStringToLocalDateTime(orderDetailResponse.getPurchase_date_local(), CommonUtils.DATETIME_FORMATTER, ZoneId.of("UTC"), ZoneId.of("Asia/Shanghai"))); // todo utc转一下
        opsOrderSaleOrder.setLastUpdateDate(CommonUtils.convertStringToLocalDateTime(orderDetailResponse.getLast_update_date(), CommonUtils.DATETIME_FORMATTER, ZoneId.of("UTC"), null));
        opsOrderSaleOrder.setLastUpdateDateUtc(CommonUtils.convertStringToLocalDateTime(orderDetailResponse.getLast_update_date(), CommonUtils.DATETIME_FORMATTER, ZoneId.of("UTC"), null));
        opsOrderSaleOrder.setPaymentMethod(orderDetailResponse.getPayment_method());
        opsOrderSaleOrder.setPostedDate(CommonUtils.convertStringToLocalDateTime(orderDetailResponse.getPosted_date(), CommonUtils.DATETIME_FORMATTER, ZoneId.of("UTC"), null));
        opsOrderSaleOrder.setPostedDateUtc(CommonUtils.convertStringToLocalDateTime(orderDetailResponse.getPosted_date(), CommonUtils.DATETIME_FORMATTER, ZoneId.of("UTC"), null));
        opsOrderSaleOrder.setPostedDateBjt(CommonUtils.convertStringToLocalDateTime(orderDetailResponse.getPosted_date(), CommonUtils.DATETIME_FORMATTER, ZoneId.of("UTC"),  ZoneId.of("Asia/Shanghai")));
        opsOrderSaleOrder.setPostedStatus(null);
        opsOrderSaleOrder.setShipServiceLevel(orderDetailResponse.getShip_service_level());
        opsOrderSaleOrder.setOrderType(orderDetailResponse.getOrder_type());
        opsOrderSaleOrder.setLatestShipDate(CommonUtils.convertStringToLocalDateTime(orderDetailResponse.getLatest_ship_date(), CommonUtils.DATETIME_FORMATTER_T_Z, ZoneId.of("UTC"), null));
        opsOrderSaleOrder.setRemark(null);
        opsOrderSaleOrder.setTrackingNo(orderDetailResponse.getTracking_number());
        opsOrderSaleOrder.setSenderTaxNo(null);

        opsOrderSaleOrder.setName(orderDetailResponse.getName());
        opsOrderSaleOrder.setPhone(orderDetailResponse.getPhone());
        opsOrderSaleOrder.setEmail(orderDetailResponse.getBuyer_email());
        opsOrderSaleOrder.setBuyerRemark(null);
        opsOrderSaleOrder.setRecipient(orderDetailResponse.getBuyer_name());
        opsOrderSaleOrder.setAddressLine1(StringUtils.firstNonBlank(orderDetailResponse.getAddress(), orderDetailResponse.getAddress_line1()));
        opsOrderSaleOrder.setAddressLine2(orderDetailResponse.getAddress_line2());
        opsOrderSaleOrder.setAddressLine3(orderDetailResponse.getAddress_line3());
        opsOrderSaleOrder.setCountry(StringUtils.firstNonBlank(orderDetailResponse.getCountry_code(), orderDetailResponse.getCountry()));
        opsOrderSaleOrder.setCity(orderDetailResponse.getCity());
        opsOrderSaleOrder.setStateProvince(orderDetailResponse.getState_or_region());
        opsOrderSaleOrder.setDistrictCounty(orderDetailResponse.getDistrict());
        opsOrderSaleOrder.setPostalCode(orderDetailResponse.getPostal_code());

        opsOrderSaleOrder.setIsBusinessOrder(Objects.equals(orderDetailResponse.getIs_business_order(), 1));
        opsOrderSaleOrder.setIsPrime(Objects.equals(orderDetailResponse.getIs_prime(), 1));
        opsOrderSaleOrder.setIsPremiumOrder(Objects.equals(orderDetailResponse.getIs_premium_order(), 1));
        opsOrderSaleOrder.setIsPromotion(Objects.equals(orderDetailResponse.getIs_promotion(), 1));
        opsOrderSaleOrder.setOrderTotalAmount(orderDetailResponse.getOrder_total_amount());
        opsOrderSaleOrder.setShippingPriceAmount(null); // todo 这个字段在sku列表里，是否要加总
        opsOrderSaleOrder.setTaxAmount(null); // todo 这个字段在sku列表里，是否要加总
        opsOrderSaleOrder.setPredictProfit(null);
        opsOrderSaleOrder.setPredictGrossProfit(null);
        opsOrderSaleOrder.setUpdateBy(OpsUserContext.getTenantId());
        opsOrderSaleOrder.setUpdateTime(LocalDateTime.now());
    }

    @Override
    protected void buildInsertOrderSku(MwsOrderResponse mwsOrderResponse, MwsOrderDetailResponse mwsOrderDetailResponse, MwsOrderDetailResponse.Item item, OpsOrderSaleOrder opsOrderSaleOrder, OpsOrderSaleOrderSku opsOrderSaleOrderSku) {
        opsOrderSaleOrderSku.setId(SnowflakeIdWorkerUtils.generateId());
        opsOrderSaleOrderSku.setTenantId(OpsUserContext.getTenantId());
        opsOrderSaleOrderSku.setAsin(item.getAsin());
        opsOrderSaleOrderSku.setMsku(item.getSku());
        opsOrderSaleOrderSku.setSku(item.getSeller_sku());
        opsOrderSaleOrderSku.setPlatform_order_no(mwsOrderDetailResponse.getAmazon_order_id());
        opsOrderSaleOrderSku.setCreateBy(OpsUserContext.getTenantId());
        opsOrderSaleOrderSku.setCreateTime(LocalDateTime.now());
        buildUpdateOrderSku(mwsOrderResponse, mwsOrderDetailResponse, item, opsOrderSaleOrder, opsOrderSaleOrderSku);
    }

    @Override
    protected void buildUpdateOrderSku(MwsOrderResponse mwsOrderResponse, MwsOrderDetailResponse mwsOrderDetailResponse, MwsOrderDetailResponse.Item item, OpsOrderSaleOrder opsOrderSaleOrder, OpsOrderSaleOrderSku opsOrderSaleOrderSku) {
        opsOrderSaleOrderSku.setTitle(item.getTitle());
        opsOrderSaleOrderSku.setProductName(item.getProduct_name());
        opsOrderSaleOrderSku.setSpecificationInfo(null);
        opsOrderSaleOrderSku.setIsCustomized(StringUtils.isNoneBlank(item.getCustomized_json()) ? 1 : 0);
        opsOrderSaleOrderSku.setOrderItemId(item.getOrder_item_id());
        opsOrderSaleOrderSku.setQuantityOrdered(item.getQuantity_ordered());
        opsOrderSaleOrderSku.setQuantityShipped(item.getQuantity_shipped());
        opsOrderSaleOrderSku.setUnitPriceAmount(item.getUnit_price_amount());
        opsOrderSaleOrderSku.setTaxAmount(item.getTax_amount());
        opsOrderSaleOrderSku.setItemPriceAmount(item.getItem_price_amount());
        opsOrderSaleOrderSku.setItemTaxAmount(item.getItem_tax_amount());
        opsOrderSaleOrderSku.setRemark(null);
        opsOrderSaleOrderSku.setShippingPriceAmount(item.getShipping_price_amount());
        opsOrderSaleOrderSku.setShippingTaxAmount(item.getShipping_tax_amount());
        opsOrderSaleOrderSku.setShippingDiscountAmount(item.getShipping_discount_amount());
        opsOrderSaleOrderSku.setShippingDiscountTaxAmount(item.getShipping_discount_tax_amount());
        opsOrderSaleOrderSku.setGiftWrapPriceAmount(item.getGift_wrap_price_amount());
        opsOrderSaleOrderSku.setGiftWrapTaxAmount(item.getGift_wrap_tax_amount());
        opsOrderSaleOrderSku.setPromotionAmount(item.getPromotion_amount());
        opsOrderSaleOrderSku.setPromotionDiscountTaxAmount(item.getPromotion_discount_tax_amount());
        opsOrderSaleOrderSku.setCodFeeAmount(item.getCod_fee_amount());
        opsOrderSaleOrderSku.setCodFeeDiscountAmount(item.getCod_fee_discount_amount());
        opsOrderSaleOrderSku.setFbaShipmentAmount(item.getFba_shipment_amount());
        opsOrderSaleOrderSku.setCommissionAmount(item.getCommission_amount());
        opsOrderSaleOrderSku.setPointsMonetaryValueAmount(item.getPoints_monetary_value_amount());
        opsOrderSaleOrderSku.setOtherAmount(item.getOther_amount());
        opsOrderSaleOrderSku.setGrossIncome(null);
        opsOrderSaleOrderSku.setInventoryCost(null);
        opsOrderSaleOrderSku.setCgPrice(item.getCg_price());
        opsOrderSaleOrderSku.setCgTransportCosts(item.getCg_transport_costs());
        opsOrderSaleOrderSku.setOtherCosts(null);
        opsOrderSaleOrderSku.setProfit(item.getProfit());
        opsOrderSaleOrderSku.setProfitRate(null);
        opsOrderSaleOrderSku.setUpdateBy(OpsUserContext.getTenantId());
        opsOrderSaleOrderSku.setUpdateTime(LocalDateTime.now());
    }

}
