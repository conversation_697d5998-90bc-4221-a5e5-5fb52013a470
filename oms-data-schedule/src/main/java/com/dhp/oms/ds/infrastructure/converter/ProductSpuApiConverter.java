package com.dhp.oms.ds.infrastructure.converter;

import com.dhp.oms.framework.context.user.OpsUserContext;
import com.dhp.oms.framework.core.entity.ops.product.OpsProductCategory;
import com.dhp.oms.framework.core.entity.ops.sku.OpsProductSkuPurchaseInfo;
import com.dhp.oms.framework.core.entity.ops.spu.ProductSpuBaseInfo;
import com.dhp.oms.framework.core.entity.ops.spu.ProductSpuClearanceFee;
import com.dhp.oms.framework.core.entity.ops.spu.ProductSpuDeclaration;
import com.dhp.oms.framework.core.entity.ops.spu.ProductSpuPurchaseInfo;
import com.dhp.oms.framework.core.entity.ops.spu.ProductSpuSpecificationInfo;
import com.dhp.oms.framework.utils.SnowflakeIdWorkerUtils;
import com.dhp.oms.third.api.module.spu.request.ProductSpuDetailResponse;
import com.dhp.oms.third.api.module.spu.response.ProductSpuResponse;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

@Mapper
public interface ProductSpuApiConverter {

    ProductSpuApiConverter INSTANCE = Mappers.getMapper(ProductSpuApiConverter.class);

    default void buildProductSpuBaseInfoInsert(ProductSpuResponse productSpuResponse,
                                               ProductSpuDetailResponse productSpuDetailResponse,
                                               Map<Long, OpsProductCategory> opsProductCategoryMap,
                                               ProductSpuBaseInfo baseInfo,
                                               List<ProductSpuBaseInfo> productSpuBaseInfoList) {
        baseInfo.setId(SnowflakeIdWorkerUtils.generateId());
        baseInfo.setPsId(productSpuResponse.getPs_id());
        baseInfo.setTenantId(OpsUserContext.getTenantId());
        baseInfo.setSpu(productSpuResponse.getSpu());
        baseInfo.setCreateTime(LocalDateTime.now());
        baseInfo.setCreateBy(OpsUserContext.getTenantId());
        buildProductSpuBaseInfoUpdate(productSpuResponse, productSpuDetailResponse, opsProductCategoryMap, baseInfo, productSpuBaseInfoList);
    }

    default void buildProductSpuBaseInfoUpdate(ProductSpuResponse productSpuResponse,
                                               ProductSpuDetailResponse productSpuDetailResponse,
                                               Map<Long, OpsProductCategory> opsProductCategoryMap,
                                               ProductSpuBaseInfo baseInfo,
                                               List<ProductSpuBaseInfo> productSpuBaseInfoList) {
        baseInfo.setSpuDescription(productSpuDetailResponse.getDescription());
        baseInfo.setSpuEngDescription(null);
        baseInfo.setProductAbbreviation(productSpuDetailResponse.getSpu_name());
        baseInfo.setBid(productSpuDetailResponse.getBid());
        baseInfo.setBrandName(productSpuDetailResponse.getBrand_name());
        baseInfo.setSpuUnit(productSpuDetailResponse.getUnit());
        baseInfo.setSpuStatus(String.valueOf(productSpuDetailResponse.getStatus()));
        baseInfo.setCid(productSpuDetailResponse.getCid());
        baseInfo.setSpuCategory(Optional.ofNullable(opsProductCategoryMap.get(productSpuDetailResponse.getCid())).map(OpsProductCategory::getCategoryCode).orElse(null));
        baseInfo.setSpuType(null);
        baseInfo.setSpuMarket(null);
        baseInfo.setProductGrade(null);
        baseInfo.setInfringementLevel(null);
        baseInfo.setDevManagerId(productSpuDetailResponse.getDeveloper_uid());
        baseInfo.setDevManager(null);
        baseInfo.setSalesManager(null);
        baseInfo.setInnerCode(null);
        baseInfo.setProductInstructionManual(null);
        baseInfo.setProductMaterialStandard(null);
        baseInfo.setProductQualityCheckStandard(null);
        baseInfo.setProductServiceStandard(null);
        baseInfo.setProductMaterial(null);
        baseInfo.setProductPackagingList(null);
        baseInfo.setUpdateTime(LocalDateTime.now());
        baseInfo.setUpdateBy(OpsUserContext.getTenantId());
        productSpuBaseInfoList.add(baseInfo);
    }

    default void buildOpsProductSpuDeclarationInsert(ProductSpuResponse productSpuResponse,
                                                     ProductSpuDetailResponse.Declaration declarationEntry,
                                                     List<ProductSpuDeclaration> productSpuDeclarationList) {
        ProductSpuDeclaration declaration = new ProductSpuDeclaration();
        declaration.setId(SnowflakeIdWorkerUtils.generateId());
        declaration.setSpuId(String.valueOf(productSpuResponse.getPs_id()));
        declaration.setTenantId(OpsUserContext.getTenantId());
        declaration.setCreateTime(LocalDateTime.now());
        declaration.setCreateBy(OpsUserContext.getTenantId());
        buildProductSpuDeclarationUpdate(productSpuResponse, declarationEntry, declaration, productSpuDeclarationList);
    }

    default void buildProductSpuDeclarationUpdate(ProductSpuResponse productSpuResponse,
                                                  ProductSpuDetailResponse.Declaration declarationEntry,
                                                  ProductSpuDeclaration declaration,
                                                  List<ProductSpuDeclaration> productSpuDeclarationList) {
        if (Objects.isNull(declarationEntry)) {
            return;
        }
        declaration.setChineseName(declarationEntry.getCustoms_export_name());
        declaration.setEnglishName(declarationEntry.getCustoms_import_name());
        declaration.setDeclarationPriceUnit(declarationEntry.getCustoms_import_price_currency());
        declaration.setDeclarationPrice(declarationEntry.getCustoms_import_price());
        declaration.setDeclarationUnit(declarationEntry.getCustoms_declaration_unit());
        declaration.setDeclarationModel(declarationEntry.getCustoms_declaration_spec());
        declaration.setOriginCountry(declarationEntry.getCustoms_declaration_origin_produce());
        declaration.setOriginPlace(declarationEntry.getCustoms_declaration_inlands_source());
        declaration.setLevyType(declarationEntry.getCustoms_declaration_exempt());
        declaration.setHsCode(declarationEntry.getCustoms_declaration_hs_code());
        declaration.setUpdateTime(LocalDateTime.now());
        declaration.setUpdateBy(OpsUserContext.getTenantId());
        productSpuDeclarationList.add(declaration);
    }

    default void buildProductSpuClearanceFeeInsert(ProductSpuResponse productSpuResponse,
                                                   ProductSpuDetailResponse.Clearance clearanceEntry,
                                                   List<ProductSpuClearanceFee> productSpuClearanceFeeList) {
        ProductSpuClearanceFee clearanceFee = new ProductSpuClearanceFee();
        clearanceFee.setId(SnowflakeIdWorkerUtils.generateId());
        clearanceFee.setSpuId(String.valueOf(productSpuResponse.getPs_id()));
        clearanceFee.setTenantId(OpsUserContext.getTenantId());
        clearanceFee.setCreateTime(LocalDateTime.now());
        clearanceFee.setCreateBy(OpsUserContext.getTenantId());
        buildProductSpuClearanceFeeUpdate(productSpuResponse, clearanceEntry, clearanceFee, productSpuClearanceFeeList);
    }

    default void buildProductSpuClearanceFeeUpdate(ProductSpuResponse productSpuResponse,
                                                   ProductSpuDetailResponse.Clearance clearanceEntry,
                                                   ProductSpuClearanceFee clearanceFee,
                                                   List<ProductSpuClearanceFee> opsProductSkuClearanceFeeList) {
        if (Objects.isNull(clearanceEntry)) {
            return;
        }
        clearanceFee.setCountryCode(null);
        clearanceFee.setDefaultFreight(null);
        clearanceFee.setDefaultFreightUnit(null);
        clearanceFee.setHsCode(clearanceEntry.getCustoms_clearance_hs_code());
        clearanceFee.setUnitPrice(Optional.ofNullable(clearanceEntry.getCustoms_clearance_price()).map(BigDecimal::new).map(BigDecimal::intValue).orElse(null));
        clearanceFee.setUnitPriceUnit(clearanceEntry.getCustoms_clearance_price_currency());
        clearanceFee.setTaxRate(Optional.ofNullable(clearanceEntry.getCustoms_clearance_tax_rate()).map(Double::parseDouble).orElse(null));
        clearanceFee.setRemark(clearanceEntry.getCustoms_clearance_remark());
        clearanceFee.setUpdateTime(LocalDateTime.now());
        clearanceFee.setUpdateBy(OpsUserContext.getTenantId());
        opsProductSkuClearanceFeeList.add(clearanceFee);
    }

    default void buildProductSpuPurchaseInfoInsert(ProductSpuResponse productSpuResponse,
                                                   ProductSpuDetailResponse.PurchaseInfo purchaseInfoEntry,
                                                   List<ProductSpuPurchaseInfo> productSpuPurchaseInfoList) {
        ProductSpuPurchaseInfo purchaseInfo = new ProductSpuPurchaseInfo();
        purchaseInfo.setId(SnowflakeIdWorkerUtils.generateId());
        purchaseInfo.setSpuId(String.valueOf(productSpuResponse.getPs_id()));
        purchaseInfo.setTenantId(OpsUserContext.getTenantId());
        purchaseInfo.setCreateTime(LocalDateTime.now());
        purchaseInfo.setCreateBy(OpsUserContext.getTenantId());
        buildProductSpuPurchaseInfoUpdate(productSpuResponse, purchaseInfoEntry, purchaseInfo, productSpuPurchaseInfoList);
    }

    default void buildProductSpuPurchaseInfoUpdate(ProductSpuResponse productSpuResponse,
                                                   ProductSpuDetailResponse.PurchaseInfo purchaseInfoEntry,
                                                   ProductSpuPurchaseInfo purchaseInfo,
                                                   List<ProductSpuPurchaseInfo> productSpuPurchaseInfoList) {
        if (Objects.isNull(purchaseInfoEntry)) {
            return;
        }
        purchaseInfo.setPurchaserId(String.valueOf(purchaseInfoEntry.getCg_uid()));
        purchaseInfo.setLeadTime(Long.valueOf(purchaseInfoEntry.getCg_delivery()));
        purchaseInfo.setUpdateTime(LocalDateTime.now());
        purchaseInfo.setUpdateBy(OpsUserContext.getTenantId());
        productSpuPurchaseInfoList.add(purchaseInfo);
    }

    default void buildProductSpuSpecificationInfoInsert(ProductSpuResponse productSpuResponse,
                                                        ProductSpuDetailResponse.PurchaseInfo purchaseInfoEntry,
                                                        List<ProductSpuSpecificationInfo> productSpuSpecificationInfoList) {
        ProductSpuSpecificationInfo spuSpecificationInfo = new ProductSpuSpecificationInfo();
        spuSpecificationInfo.setId(SnowflakeIdWorkerUtils.generateId());
        spuSpecificationInfo.setSpuId(String.valueOf(productSpuResponse.getPs_id()));
        spuSpecificationInfo.setTenantId(OpsUserContext.getTenantId());
        spuSpecificationInfo.setCreateTime(LocalDateTime.now());
        spuSpecificationInfo.setCreateBy(OpsUserContext.getTenantId());
        buildProductSpuSpecificationInfoUpdate(productSpuResponse, purchaseInfoEntry, spuSpecificationInfo, productSpuSpecificationInfoList);
    }

    default void buildProductSpuSpecificationInfoUpdate(ProductSpuResponse productSpuResponse,
                                                        ProductSpuDetailResponse.PurchaseInfo purchaseInfoEntry,
                                                        ProductSpuSpecificationInfo spuSpecificationInfo,
                                                        List<ProductSpuSpecificationInfo> productSpuSpecificationInfoList) {
        if (Objects.isNull(purchaseInfoEntry)) {
            return;
        }
        spuSpecificationInfo.setSpuGlobalLength(Optional.ofNullable(purchaseInfoEntry.getCg_product_length()).map(BigDecimal::toPlainString).orElse(null));
        spuSpecificationInfo.setSpuGlobalWide(Optional.ofNullable(purchaseInfoEntry.getCg_product_width()).map(BigDecimal::toPlainString).orElse(null));
        spuSpecificationInfo.setSpuGlobalHigh(Optional.ofNullable(purchaseInfoEntry.getCg_product_height()).map(BigDecimal::toPlainString).orElse(null));
        spuSpecificationInfo.setSpuGlobalSingleNetWeight(Optional.ofNullable(purchaseInfoEntry.getCg_product_net_weight()).map(BigDecimal::toPlainString).orElse(null));
        spuSpecificationInfo.setSpuGlobalSingleWeight(Optional.ofNullable(purchaseInfoEntry.getCg_product_gross_weight()).map(BigDecimal::toPlainString).orElse(null));
        spuSpecificationInfo.setSpuPackageBoxLength(Optional.ofNullable(purchaseInfoEntry.getCg_package_length()).map(BigDecimal::toPlainString).orElse(null));
        spuSpecificationInfo.setSpuPackageBoxWide(Optional.ofNullable(purchaseInfoEntry.getCg_package_width()).map(BigDecimal::toPlainString).orElse(null));
        spuSpecificationInfo.setSpuPackageBoxHigh(Optional.ofNullable(purchaseInfoEntry.getCg_package_height()).map(BigDecimal::toPlainString).orElse(null));
        spuSpecificationInfo.setSpuOuterBoxLength(Optional.ofNullable(purchaseInfoEntry.getCg_box_length()).map(BigDecimal::toPlainString).orElse(null));
        spuSpecificationInfo.setSpuOuterBoxWide(Optional.ofNullable(purchaseInfoEntry.getCg_box_width()).map(BigDecimal::toPlainString).orElse(null));
        spuSpecificationInfo.setSpuOuterBoxHigh(Optional.ofNullable(purchaseInfoEntry.getCg_box_height()).map(BigDecimal::toPlainString).orElse(null));
        spuSpecificationInfo.setSpuSingleBoxWeight(Optional.ofNullable(purchaseInfoEntry.getCg_box_weight()).map(BigDecimal::toPlainString).orElse(null));
        spuSpecificationInfo.setUpdateTime(LocalDateTime.now());
        spuSpecificationInfo.setUpdateBy(OpsUserContext.getTenantId());
        productSpuSpecificationInfoList.add(spuSpecificationInfo);
    }

}
