package com.dhp.oms.ds.infrastructure.mapper.spu;

import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import com.dhp.oms.framework.context.user.OpsUserContext;
import com.dhp.oms.framework.core.entity.ops.spu.ProductSpuDeclaration;
import com.dhp.oms.framework.core.entity.ops.spu.ProductSpuPurchaseInfo;
import com.dhp.oms.framework.core.mapper.AbstractBaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <p>
 * SPU采购主信息表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-05
 */
@Mapper
public interface ProductSpuPurchaseInfoMapper extends AbstractBaseMapper<ProductSpuPurchaseInfo> {

    default Boolean deleteByNotIdList(List<Long> notInPsIdList) {
        return new LambdaUpdateChainWrapper<>(this)
                .notIn(ProductSpuPurchaseInfo::getSpuId, notInPsIdList)
                .eq(ProductSpuPurchaseInfo::getTenantId, OpsUserContext.getTenantId())
                .set(ProductSpuPurchaseInfo::getDeleted, Boolean.TRUE)
                .update();
    }

}
