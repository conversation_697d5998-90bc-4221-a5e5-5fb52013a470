package com.dhp.oms.ds.infrastructure.mapper.order;

import com.dhp.oms.framework.context.user.OpsUserContext;
import com.dhp.oms.framework.core.entity.ops.order.OpsOrderSaleOrderSku;
import com.dhp.oms.framework.core.mapper.AbstractBaseMapper;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.ibatis.annotations.Mapper;

import java.util.Collections;
import java.util.List;

@Mapper
public interface OpsOrderSaleOrderSkuMapper extends AbstractBaseMapper<OpsOrderSaleOrderSku> {

    default List<OpsOrderSaleOrderSku> listByOmsOrderNos(List<String> omsOrderNoList) {
        if (CollectionUtils.isEmpty(omsOrderNoList)) {
            return Collections.emptyList();
        }
        return chainQuery()
                .eq(OpsOrderSaleOrderSku::getTenantId, OpsUserContext.getTenantId())
                .in(OpsOrderSaleOrderSku::getPlatform_order_no, omsOrderNoList)
                .list();
    }

}
