package com.dhp.oms.ds.infrastructure.service.impl;


import com.dhp.oms.ds.infrastructure.service.AbstractAfsOrderSyncService;
import com.dhp.oms.framework.core.entity.ops.afs.OpsOrderAfsOrder;
import com.dhp.oms.framework.core.entity.ops.afs.OpsOrderAfsOrderItem;
import com.dhp.oms.third.api.common.ExternApiResult;
import com.dhp.oms.third.api.module.afs.AfsApi;
import com.dhp.oms.third.api.module.afs.request.MpReturnsWorkOrderListRequest;
import com.dhp.oms.third.api.module.afs.response.MpReturnsWorkOrderListResponse;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.BiConsumer;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service("afsOrderSync2ServiceImpl")
public class AfsOrderSync2ServiceImpl extends AbstractAfsOrderSyncService<MpReturnsWorkOrderListRequest, MpReturnsWorkOrderListResponse, MpReturnsWorkOrderListResponse, MpReturnsWorkOrderListResponse.Rma_info> {

    @Resource
    private AfsApi afsApi;

    @Override
    protected MpReturnsWorkOrderListRequest newOrderListRequestInstance(List<String> partitionSidList) {
        return new MpReturnsWorkOrderListRequest();
    }

    @Override
    protected boolean firstPage(MpReturnsWorkOrderListRequest mpReturnsWorkOrderListRequest) {
        return mpReturnsWorkOrderListRequest.getOffset() == 0;
    }

    @Override
    protected boolean hasNextPage(MpReturnsWorkOrderListRequest mpReturnsWorkOrderListRequest, Long total) {
        return mpReturnsWorkOrderListRequest.getOffset() < total;
    }

    @Override
    protected void setNextPage(MpReturnsWorkOrderListRequest mpReturnsWorkOrderListRequest) {
        mpReturnsWorkOrderListRequest.setOffset(mpReturnsWorkOrderListRequest.getOffset() + mpReturnsWorkOrderListRequest.getLength());
    }

    @Override
    protected List<MpReturnsWorkOrderListResponse> getOrderList(ExternApiResult<?> result) {
        return Optional.ofNullable(result)
                .map(item -> (ExternApiResult<List<MpReturnsWorkOrderListResponse>>) item)
                .map(ExternApiResult::getData)
                .orElse(Collections.emptyList());
    }

    @Override
    protected Function<MpReturnsWorkOrderListResponse, String> obtainGetOrderIdFunc() {
        return MpReturnsWorkOrderListResponse::getOrder_number;
    }

    @Override
    protected BiConsumer<MpReturnsWorkOrderListResponse, Long> obtainSetBizOrderIdFunc() {
        return MpReturnsWorkOrderListResponse::setBizOrderId;
    }

    @Override
    protected Function<MpReturnsWorkOrderListResponse, Long> obtainGetBizOrderIdFunc() {
        return MpReturnsWorkOrderListResponse::getBizOrderId;
    }

    @Override
    protected Function<MpReturnsWorkOrderListResponse.Rma_info, String> obtainGetOrderItemIdFunc() {
        return MpReturnsWorkOrderListResponse.Rma_info::getMsku;
    }

    @Override
    protected BiConsumer<MpReturnsWorkOrderListResponse.Rma_info, Long> obtainSetBizOrderItemIdFunc() {
        return MpReturnsWorkOrderListResponse.Rma_info::setBizOrderItemId;
    }

    @Override
    protected Function<MpReturnsWorkOrderListResponse.Rma_info, Long> obtainGetBizOrderItemIdFunc() {
        return null;
    }

    @Override
    protected List<MpReturnsWorkOrderListResponse.Rma_info> obtainOrderSkuList(MpReturnsWorkOrderListResponse mpReturnsWorkOrderListResponse, MpReturnsWorkOrderListResponse mpReturnsWorkOrderListResponse2) {
        return mpReturnsWorkOrderListResponse.getRma_info();
    }

    @Override
    protected ExternApiResult<?> getOrderResponse(MpReturnsWorkOrderListRequest mpReturnsWorkOrderListRequest, LocalDateTime beginTime, LocalDateTime endTime) {
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        mpReturnsWorkOrderListRequest.setStart_time(dateTimeFormatter.format(beginTime));
        mpReturnsWorkOrderListRequest.setEnd_time(dateTimeFormatter.format(endTime));
        return afsApi.mpReturnsWorkOrderList(mpReturnsWorkOrderListRequest);
    }

    @Override
    protected Map<String, MpReturnsWorkOrderListResponse> getOrderDetailResponse(List<MpReturnsWorkOrderListResponse> orderList) {
        return orderList.stream()
                .collect(Collectors.toMap(
                        obtainGetOrderIdFunc(),
                        Function.identity(),
                        (pre, next) -> next));
    }

    @Override
    protected void buildInsertOrder(MpReturnsWorkOrderListRequest mpReturnsWorkOrderListRequest, MpReturnsWorkOrderListResponse mpReturnsWorkOrderListResponse, MpReturnsWorkOrderListResponse mpReturnsWorkOrderListResponse2, OpsOrderAfsOrder opsOrderAfsOrder) {

    }

    @Override
    protected void buildUpdateOrder(MpReturnsWorkOrderListResponse mpReturnsWorkOrderListResponse, MpReturnsWorkOrderListResponse mpReturnsWorkOrderListResponse2, OpsOrderAfsOrder opsOrderAfsOrder) {

    }

    @Override
    protected void buildInsertOrderSku(MpReturnsWorkOrderListResponse mpReturnsWorkOrderListResponse, MpReturnsWorkOrderListResponse mpReturnsWorkOrderListResponse2, MpReturnsWorkOrderListResponse.Rma_info rmaInfo, OpsOrderAfsOrder opsOrderAfsOrder, OpsOrderAfsOrderItem opsOrderAfsOrderItem) {

    }

    @Override
    protected void buildUpdateOrderSku(MpReturnsWorkOrderListResponse mpReturnsWorkOrderListResponse, MpReturnsWorkOrderListResponse mpReturnsWorkOrderListResponse2, MpReturnsWorkOrderListResponse.Rma_info rmaInfo, OpsOrderAfsOrder opsOrderAfsOrder, OpsOrderAfsOrderItem opsOrderAfsOrderItem) {

    }
}
