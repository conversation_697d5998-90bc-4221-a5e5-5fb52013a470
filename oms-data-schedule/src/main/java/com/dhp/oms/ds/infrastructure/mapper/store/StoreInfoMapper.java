package com.dhp.oms.ds.infrastructure.mapper.store;

import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.dhp.oms.framework.context.tenant.TenentContext;
import com.dhp.oms.framework.context.user.OpsUserContext;
import com.dhp.oms.framework.core.entity.ops.store.StoreInfo;
import com.dhp.oms.framework.core.mapper.AbstractBaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <p>
 * 店铺信息 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-17
 */
@Mapper
public interface StoreInfoMapper extends AbstractBaseMapper<StoreInfo> {

    default List<StoreInfo> listByTenantId(Long tenantId) {
        return new LambdaQueryChainWrapper<>(this)
                .eq(StoreInfo::getDeleted, 0)
                .list();
    }

}
