package com.dhp.oms.ds.infrastructure.mapper.sku;

import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import com.dhp.oms.framework.context.user.OpsUserContext;
import com.dhp.oms.framework.core.entity.ops.sku.OpsProductSkuDeclaration;
import com.dhp.oms.framework.core.entity.ops.sku.OpsProductSkuPurchaseInfo;
import com.dhp.oms.framework.core.mapper.AbstractBaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface OpsProductSkuPurchaseInfoMapper extends AbstractBaseMapper<OpsProductSkuPurchaseInfo> {

    default Boolean deleteByNotIdList(List<String> notInSkuIdList) {
        return new LambdaUpdateChainWrapper<>(this)
                .notIn(OpsProductSkuPurchaseInfo::getSkuId, notInSkuIdList)
                .eq(OpsProductSkuPurchaseInfo::getTenantId, OpsUserContext.getTenantId())
                .set(OpsProductSkuPurchaseInfo::getDeleted, Boolean.TRUE)
                .update();
    }

}
