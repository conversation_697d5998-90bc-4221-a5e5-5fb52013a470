package com.dhp.oms.ds.infrastructure.service.impl;

import com.alibaba.fastjson.JSON;
import com.dhp.oms.ds.infrastructure.mapper.email.OpsErpEmailAnalyzeInfoMapper;
import com.dhp.oms.ds.infrastructure.mapper.email.OpsErpEmailInfoMapper;
import com.dhp.oms.ds.infrastructure.mapper.email.OpsErpEmailOrderInfoMapper;
import com.dhp.oms.ds.infrastructure.service.CornEmailService;
import com.dhp.oms.framework.core.entity.ops.email.OpsErpEmailAnalyzeInfo;
import com.dhp.oms.framework.core.entity.ops.email.OpsErpEmailInfo;
import com.dhp.oms.framework.core.entity.ops.email.OpsErpEmailOrderInfo;
import com.dhp.oms.framework.utils.CommonUtils;
import com.dhp.oms.third.api.extern.entity.email.EmailInfo;
import com.dhp.oms.third.api.module.ai.OmsAiService;
import com.dhp.oms.third.api.module.email.EmailServiceApi;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Strings;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;



@Service
@Slf4j
public class CornEmailServiceImpl implements CornEmailService {


    @Resource
    private OpsErpEmailInfoMapper opsErpEmailInfoMapper;

    @Resource
    private OmsAiService omsAiService;

    @Resource
    private OpsErpEmailAnalyzeInfoMapper opsErpEmailAnalyzeInfoMapper;


    @Resource
    private EmailServiceApi emailServiceApi;

    @Resource
    private OpsErpEmailOrderInfoMapper opsErpEmailOrderInfoMapper;


    private static final ExecutorService EMAIL_PROCESSOR_POOL =
            Executors.newFixedThreadPool(Runtime.getRuntime().availableProcessors() * 2);


    @Override
    public void loadEmailList() {
        List<EmailInfo> emailReceiveInfos = emailServiceApi.GetReceiveEmailList(0, 1000,"<EMAIL>","2025-01-01","2025-12-31");
        List<EmailInfo> emailSentInfos = emailServiceApi.GetSentEmailList(0, 1000);
        List<OpsErpEmailInfo> result = new ArrayList<>();
        List<OpsErpEmailOrderInfo> emailOrderInfos = new ArrayList<>();

        if (CollectionUtils.isNotEmpty(emailReceiveInfos)) {
            for (EmailInfo emailInfo : emailReceiveInfos) {
                OpsErpEmailInfo opsErpEmailInfo = new OpsErpEmailInfo();
                String webmailUuid = emailInfo.getWebmailUuid();
                String subject = emailInfo.getSubject();
                opsErpEmailInfo.setEmailDate(CommonUtils.convertStringToLocalDateTimeNew(emailInfo.getDate()));
                opsErpEmailInfo.setFlag("receive");
                opsErpEmailInfo.setWebmailUuid(webmailUuid);
                opsErpEmailInfo.setToName(emailInfo.getToName());
                opsErpEmailInfo.setSubject(subject);
                opsErpEmailInfo.setToAddress(emailInfo.getToAddress());
                opsErpEmailInfo.setHasAttachment(emailInfo.isHasAttachment());
                opsErpEmailInfo.setFromName(emailInfo.getFromName());
                opsErpEmailInfo.setFromAddress(emailInfo.getFromAddress());
                result.add(opsErpEmailInfo);
                List<String> orderNumbers = extractOrderNumbers(subject);
                if (!CollectionUtils.isEmpty(orderNumbers)) {
                    for (String orderNumber : orderNumbers) {
                        OpsErpEmailOrderInfo opsErpEmailOrderInfo = new OpsErpEmailOrderInfo();
                        opsErpEmailOrderInfo.setWebmailUuid(webmailUuid);
                        String uuidOrderId = webmailUuid + "-" + orderNumber;
                        opsErpEmailOrderInfo.setUuidOrderId(uuidOrderId);
                        opsErpEmailOrderInfo.setEmailDate(opsErpEmailInfo.getEmailDate());
                        opsErpEmailOrderInfo.setEmailOrderId(orderNumber);
                        emailOrderInfos.add(opsErpEmailOrderInfo);
                    }
                }
            }
        }
        if  (CollectionUtils.isNotEmpty(emailSentInfos)) {
            for(EmailInfo sentEmailInfo : emailSentInfos) {
                OpsErpEmailInfo opsErpEmailInfo = new OpsErpEmailInfo();
                opsErpEmailInfo.setEmailDate(CommonUtils.convertStringToLocalDateTimeNew(sentEmailInfo.getDate()));
                opsErpEmailInfo.setFlag("sent");
                opsErpEmailInfo.setWebmailUuid(sentEmailInfo.getWebmailUuid());
                opsErpEmailInfo.setToName(sentEmailInfo.getToName());
                opsErpEmailInfo.setSubject(sentEmailInfo.getSubject());
                opsErpEmailInfo.setToAddress(sentEmailInfo.getToAddress());
                opsErpEmailInfo.setHasAttachment(sentEmailInfo.isHasAttachment());
                opsErpEmailInfo.setFromName(sentEmailInfo.getFromName());
                opsErpEmailInfo.setFromAddress(sentEmailInfo.getFromAddress());
                result.add(opsErpEmailInfo);
            }
        }
        if(CollectionUtils.isEmpty(result)) {
            return;
        }

        List<CompletableFuture<Void>> futures = ListUtils.partition(result, 100).stream()
                .map(batch -> CompletableFuture.runAsync(() -> {
                    try {
                        List<String> uuidList = batch.stream()
                                .map(OpsErpEmailInfo::getWebmailUuid)
                                .collect(Collectors.toList());
                        List<OpsErpEmailInfo> existRecords = opsErpEmailInfoMapper.selectByWebmailUuids(uuidList);
                        Set<String> existUuidSet = existRecords.stream()
                                .map(OpsErpEmailInfo::getWebmailUuid)
                                .collect(Collectors.toSet());
                        List<OpsErpEmailInfo> toInsert = batch.stream()
                                .filter(item -> !existUuidSet.contains(item.getWebmailUuid()))
                                .collect(Collectors.toList());
                        if (!toInsert.isEmpty()) {
                            opsErpEmailInfoMapper.insertBatch(toInsert, 100);
                        }
                    } catch (Exception e) {
                        log.error("邮件处理批次失败: {}", batch, e);
                        throw new RuntimeException(e); // 确保CompletableFuture感知异常
                    }
                }, EMAIL_PROCESSOR_POOL))
                .toList();
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
        if (CollectionUtils.isNotEmpty(emailOrderInfos)) {
            ListUtils.partition(emailOrderInfos, 100).stream().toList().forEach(batch -> {
                List<String> uuidOrderIds = batch.stream().map(OpsErpEmailOrderInfo::getUuidOrderId).toList();
                List<OpsErpEmailOrderInfo> existRecords = opsErpEmailOrderInfoMapper.selectByUuidOrderIds(uuidOrderIds);
                if (!existRecords.isEmpty()) {
                    List<String> existsEmailUuidOrderIds = existRecords.stream().map(OpsErpEmailOrderInfo::getUuidOrderId).toList();
                    List<OpsErpEmailOrderInfo> needToInsertList = batch.stream().filter(item -> !existsEmailUuidOrderIds.contains(item.getUuidOrderId())).toList();
                    if (!needToInsertList.isEmpty()) {
                        opsErpEmailOrderInfoMapper.insertBatch(needToInsertList, 100);
                    }
                }else {
                    opsErpEmailOrderInfoMapper.insertBatch(batch, 100);
                }
            });
        }
    }

    @Override
    public void loadEmailDetail() {
        List<OpsErpEmailInfo> opsErpEmailInfos = opsErpEmailInfoMapper.searchReciveEmailList();
        if (CollectionUtils.isEmpty(opsErpEmailInfos)) {
            return ;
        }
        List<CompletableFuture<Void>> futures = ListUtils.partition(opsErpEmailInfos, 100).stream()
                .map(batch -> CompletableFuture.runAsync(() -> {
                    try {
                        Set<String> uuidList = batch.stream()
                                .map(OpsErpEmailInfo::getWebmailUuid)
                                .collect(Collectors.toSet());
                        List<OpsErpEmailAnalyzeInfo> existRecords = opsErpEmailAnalyzeInfoMapper.selectByWebmailUuids(uuidList);
                        Set<String> existUuidSet = existRecords.stream()
                                .map(OpsErpEmailAnalyzeInfo::getWebmailUuid)
                                .collect(Collectors.toSet());
                        List<OpsErpEmailInfo> toInsert = batch.stream()
                                .filter(item -> !existUuidSet.contains(item.getWebmailUuid()))
                                .toList();
                        if (!toInsert.isEmpty()) {
                            List<OpsErpEmailAnalyzeInfo> inertedList = new ArrayList<>();
                            for (OpsErpEmailInfo opsErpEmailInfo : toInsert) {
                                String webmailUuid = opsErpEmailInfo.getWebmailUuid();
                                String textPlain = emailServiceApi.GetEmailDetail(webmailUuid);
                                if (Strings.isNullOrEmpty(textPlain)) {
                                    textPlain = "";
                                }
                                List<Map<String, String>> maps = omsAiService.CallWithMessageSupport(textPlain);
                                String emailAnalyze;
                                if (CollectionUtils.isEmpty(maps)) {
                                    emailAnalyze = "";
                                }else {
                                    emailAnalyze =  JSON.toJSONString(maps);
                                }
                                OpsErpEmailAnalyzeInfo opsErpEmailAnalyzeInfo = getOpsErpEmailAnalyzeInfo(opsErpEmailInfo, textPlain, emailAnalyze);
                                inertedList.add(opsErpEmailAnalyzeInfo);
                            }
                            opsErpEmailAnalyzeInfoMapper.insertBatch(inertedList, 100);
                        }
                    } catch (Exception e) {
                        log.error("邮件解析入库处理批次失败: {}", batch, e);
                        throw new RuntimeException(e); // 确保CompletableFuture感知异常
                    }
                }, EMAIL_PROCESSOR_POOL))
                .toList();
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
    }

    public static List<String> extractOrderNumbers(String text) {
        try{
            Set<String> orderNumbers = new HashSet<>();
            // 正则表达式匹配格式：数字-数字-数字（至少包含两个连字符）
            Pattern pattern = Pattern.compile("\\b\\d+-\\d+-\\d+\\b");
            Matcher matcher = pattern.matcher(text);
            while (matcher.find()) {
                String match = matcher.group();
                orderNumbers.add(match);
            }
            return orderNumbers.stream()
                    .toList();
        }catch(Exception e){
            return Collections.emptyList();
        }
    }
    private static OpsErpEmailAnalyzeInfo getOpsErpEmailAnalyzeInfo(OpsErpEmailInfo opsErpEmailInfo, String textPlain, String emailAnalyze) {
        OpsErpEmailAnalyzeInfo opsErpEmailAnalyzeInfo = new OpsErpEmailAnalyzeInfo();
        opsErpEmailAnalyzeInfo.setWebmailUuid(opsErpEmailInfo.getWebmailUuid());
        opsErpEmailAnalyzeInfo.setEmailDate(opsErpEmailInfo.getEmailDate());
        opsErpEmailAnalyzeInfo.setTextPlain(textPlain);
        opsErpEmailAnalyzeInfo.setTextPlainAnalyze(emailAnalyze);
        //邮件解析情况 1 解析成功 0 解析失败
        if (StringUtils.equals(emailAnalyze,"")) {
            opsErpEmailAnalyzeInfo.setAnalyzeFlag("0");
        }else {
            opsErpEmailAnalyzeInfo.setAnalyzeFlag("1");
        }
        return opsErpEmailAnalyzeInfo;
    }
}
