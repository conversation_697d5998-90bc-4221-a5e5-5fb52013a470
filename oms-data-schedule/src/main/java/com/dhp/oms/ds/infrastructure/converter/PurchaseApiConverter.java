package com.dhp.oms.ds.infrastructure.converter;

import com.dhp.oms.framework.core.entity.ops.purchase.PurchasePlan;
import com.dhp.oms.framework.core.entity.ops.purchase.PurchaseRequestSku;
import com.dhp.oms.third.api.module.purchase.response.GetPurchasePlansResponse;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.time.LocalDateTime;

@Mapper
public interface PurchaseApiConverter {

    PurchaseApiConverter INSTANCE = Mappers.getMapper(PurchaseApiConverter.class);

    default PurchasePlan convertPurchasePlan(GetPurchasePlansResponse response) {
        PurchasePlan purchasePlan = toPurchasePlan(response);
        purchasePlan.setPpgSn(response.getPpg_sn());
        purchasePlan.setPlanSn(response.getPlan_sn());
        purchasePlan.setStatusText(response.getStatus_text());
        purchasePlan.setSupplierId(response.getSupplier_id());
        purchasePlan.setProductId(response.getProduct_id());
        purchasePlan.setCgBoxPcs(response.getCg_box_pcs());
        purchasePlan.setQuantityPlan(response.getQuantity_plan());
        if (response.getExpect_arrive_time() != null) {
            LocalDateTime expectArriveTime = response.getExpect_arrive_time().atStartOfDay();
            purchasePlan.setExpectArriveTime(expectArriveTime);
        }
        purchasePlan.setLingxingCreatorUid(response.getCreator_uid());
        purchasePlan.setLingxingCreateTime(response.getCreate_time());
        purchasePlan.setLingxingCreatorRealName(response.getCreator_real_name());
        return purchasePlan;
    }

    PurchasePlan toPurchasePlan(GetPurchasePlansResponse response);

    default PurchaseRequestSku convertPurchaseRequestSku(GetPurchasePlansResponse response) {
        PurchaseRequestSku requestSku = toPurchaseRequestSku(response);
        requestSku.setPpgSn(response.getPpg_sn());
        requestSku.setPlanSn(response.getPlan_sn());
        requestSku.setSkuQuantityPerBox(requestSku.getSkuQuantityPerBox());
        requestSku.setQuantityPlan(response.getQuantity_plan());
        if (response.getExpect_arrive_time() != null) {
            LocalDateTime expectArriveTime = response.getExpect_arrive_time().atStartOfDay();
            requestSku.setExpectArriveTime(expectArriveTime);
        }
        return requestSku;
    }

    PurchaseRequestSku toPurchaseRequestSku(GetPurchasePlansResponse response);

}
