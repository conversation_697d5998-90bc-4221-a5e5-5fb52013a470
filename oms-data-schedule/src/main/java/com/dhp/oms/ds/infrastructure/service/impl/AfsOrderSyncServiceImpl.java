package com.dhp.oms.ds.infrastructure.service.impl;


import com.alibaba.fastjson2.JSON;
import com.dhp.oms.ds.infrastructure.service.AbstractAfsOrderSyncService;
import com.dhp.oms.framework.context.user.OpsUserContext;
import com.dhp.oms.framework.core.entity.ops.afs.OpsOrderAfsOrder;
import com.dhp.oms.framework.core.entity.ops.afs.OpsOrderAfsOrderItem;
import com.dhp.oms.framework.core.entity.ops.store.StoreInfo;
import com.dhp.oms.framework.utils.CommonUtils;
import com.dhp.oms.framework.utils.SnowflakeIdWorkerUtils;
import com.dhp.oms.third.api.common.ExternApiResult;
import com.dhp.oms.third.api.module.afs.AfsApi;
import com.dhp.oms.third.api.module.afs.request.AmzodOrderAfterSaleListRequest;
import com.dhp.oms.third.api.module.afs.response.AmzodOrderAfterSaleListResponse;
import com.google.common.collect.Lists;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.BiConsumer;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service("afsOrderSyncServiceImpl")
public class AfsOrderSyncServiceImpl extends AbstractAfsOrderSyncService<AmzodOrderAfterSaleListRequest, AmzodOrderAfterSaleListResponse, AmzodOrderAfterSaleListResponse, AmzodOrderAfterSaleListResponse.Item> {

    @Resource
    private AfsApi afsApi;

    @Override
    public List<List<String>> partitionSidList(List<StoreInfo> storeInfoList) {
        List<String> sid = storeInfoList.stream()
                .map(StoreInfo::getSid)
                .distinct()
                .toList();
        return Lists.partition(sid, 20);
    }

    @Override
    protected AmzodOrderAfterSaleListRequest newOrderListRequestInstance(List<String> partitionSidList) {
        AmzodOrderAfterSaleListRequest request = new AmzodOrderAfterSaleListRequest();
        request.setSid(String.join(",", partitionSidList));
        return request;
    }

    @Override
    protected boolean firstPage(AmzodOrderAfterSaleListRequest amzodOrderAfterSaleListRequest) {
        return amzodOrderAfterSaleListRequest.getOffset() == 0;
    }

    @Override
    protected boolean hasNextPage(AmzodOrderAfterSaleListRequest amzodOrderAfterSaleListRequest, Long total) {
        return amzodOrderAfterSaleListRequest.getOffset() < total;
    }

    @Override
    protected void setNextPage(AmzodOrderAfterSaleListRequest amzodOrderAfterSaleListRequest) {
        amzodOrderAfterSaleListRequest.setOffset(amzodOrderAfterSaleListRequest.getOffset() + amzodOrderAfterSaleListRequest.getLength());
    }

    @Override
    protected List<AmzodOrderAfterSaleListResponse> getOrderList(ExternApiResult<?> result) {
        return Optional.ofNullable(result)
                .map(item -> (ExternApiResult<List<AmzodOrderAfterSaleListResponse>>) item)
                .map(ExternApiResult::getData)
                .orElse(Collections.emptyList());
    }

    @Override
    protected Function<AmzodOrderAfterSaleListResponse, String> obtainGetOrderIdFunc() {
        return AmzodOrderAfterSaleListResponse::getId;
    }

    @Override
    protected BiConsumer<AmzodOrderAfterSaleListResponse, Long> obtainSetBizOrderIdFunc() {
        return AmzodOrderAfterSaleListResponse::setBizOrderId;
    }

    @Override
    protected Function<AmzodOrderAfterSaleListResponse, Long> obtainGetBizOrderIdFunc() {
        return AmzodOrderAfterSaleListResponse::getBizOrderId;
    }

    @Override
    protected Function<AmzodOrderAfterSaleListResponse.Item, String> obtainGetOrderItemIdFunc() {
        return AmzodOrderAfterSaleListResponse.Item::getItem_identifier;
    }

    @Override
    protected BiConsumer<AmzodOrderAfterSaleListResponse.Item, Long> obtainSetBizOrderItemIdFunc() {
        return AmzodOrderAfterSaleListResponse.Item::setBizOrderItemId;
    }

    @Override
    protected Function<AmzodOrderAfterSaleListResponse.Item, Long> obtainGetBizOrderItemIdFunc() {
        return AmzodOrderAfterSaleListResponse.Item::getBizOrderItemId;
    }

    @Override
    protected List<AmzodOrderAfterSaleListResponse.Item> obtainOrderSkuList(AmzodOrderAfterSaleListResponse amzodOrderAfterSaleListResponse, AmzodOrderAfterSaleListResponse amzodOrderAfterSaleListResponse2) {
        return amzodOrderAfterSaleListResponse.getItem_list();
    }

    @Override
    protected ExternApiResult<?> getOrderResponse(AmzodOrderAfterSaleListRequest amzodOrderAfterSaleListRequest, LocalDateTime beginTime, LocalDateTime endTime) {
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        amzodOrderAfterSaleListRequest.setStart_date(dateTimeFormatter.format(beginTime));
        amzodOrderAfterSaleListRequest.setEnd_date(dateTimeFormatter.format(endTime));
        return afsApi.amzodOrderAfterSaleList(amzodOrderAfterSaleListRequest);
    }

    @Override
    protected Map<String, AmzodOrderAfterSaleListResponse> getOrderDetailResponse(List<AmzodOrderAfterSaleListResponse> orderList) {
        return orderList.stream()
                .collect(Collectors.toMap(
                        obtainGetOrderIdFunc(),
                        Function.identity(),
                        (pre, next) -> next));
    }

    @Override
    protected void buildInsertOrder(AmzodOrderAfterSaleListRequest amzodOrderAfterSaleListRequest, AmzodOrderAfterSaleListResponse amzodOrderAfterSaleListResponse, AmzodOrderAfterSaleListResponse amzodOrderAfterSaleListResponse2, OpsOrderAfsOrder opsOrderAfsOrder) {
        opsOrderAfsOrder.setId(SnowflakeIdWorkerUtils.generateId());
        opsOrderAfsOrder.setTenantId(OpsUserContext.getTenantId());
        opsOrderAfsOrder.setSid(amzodOrderAfterSaleListResponse.getSid());
        opsOrderAfsOrder.setPlatform("Amazon");
        opsOrderAfsOrder.setOrderNo(amzodOrderAfterSaleListResponse.getId());
        opsOrderAfsOrder.setStatus(null);
        opsOrderAfsOrder.setAutitId(null);
        opsOrderAfsOrder.setOrderType(amzodOrderAfterSaleListResponse.getIs_mcf_order());
        opsOrderAfsOrder.setReason(null);
        opsOrderAfsOrder.setSaleOrderNo(null);
        opsOrderAfsOrder.setPlatformOrderNo(amzodOrderAfterSaleListResponse.getAmazon_order_id());
        opsOrderAfsOrder.setOmsOrderNo(null);
        opsOrderAfsOrder.setSalePlatform("Amazon");
        opsOrderAfsOrder.setOrderFrom(null);
        opsOrderAfsOrder.setReturnType(null);
        opsOrderAfsOrder.setTag(CollectionUtils.isNotEmpty(amzodOrderAfterSaleListResponse.getAfter_type_tag()) ? JSON.toJSONString(amzodOrderAfterSaleListResponse.getAfter_type_tag()) : null);
        opsOrderAfsOrder.setCreateBy(OpsUserContext.getTenantId());
        opsOrderAfsOrder.setCreateTime(LocalDateTime.now());
        buildUpdateOrder(amzodOrderAfterSaleListResponse, amzodOrderAfterSaleListResponse, opsOrderAfsOrder);
    }

    @Override
    protected void buildUpdateOrder(AmzodOrderAfterSaleListResponse amzodOrderAfterSaleListResponse, AmzodOrderAfterSaleListResponse amzodOrderAfterSaleListResponse2, OpsOrderAfsOrder opsOrderAfsOrder) {
        opsOrderAfsOrder.setOrderCreateTime(CommonUtils.convertStringToLocalDateTime(amzodOrderAfterSaleListResponse.getPurchase_time(), CommonUtils.DATETIME_FORMATTER, ZoneId.of("UTC"), null));
        opsOrderAfsOrder.setOrderUpdateTime(CommonUtils.convertStringToLocalDateTime(amzodOrderAfterSaleListResponse.getGmt_modified(), CommonUtils.DATETIME_FORMATTER, ZoneId.of("UTC"), null));
        opsOrderAfsOrder.setOrderCompleteTime(CommonUtils.convertStringToLocalDateTime(amzodOrderAfterSaleListResponse.getDeal_time(), CommonUtils.DATETIME_FORMATTER, ZoneId.of("UTC"), null));
        opsOrderAfsOrder.setUpdateBy(OpsUserContext.getTenantId());
        opsOrderAfsOrder.setUpdateTime(LocalDateTime.now());
    }

    @Override
    protected void buildInsertOrderSku(AmzodOrderAfterSaleListResponse amzodOrderAfterSaleListResponse, AmzodOrderAfterSaleListResponse amzodOrderAfterSaleListResponse2, AmzodOrderAfterSaleListResponse.Item item, OpsOrderAfsOrder opsOrderAfsOrder, OpsOrderAfsOrderItem opsOrderAfsOrderItem) {
        opsOrderAfsOrderItem.setId(SnowflakeIdWorkerUtils.generateId());
        opsOrderAfsOrderItem.setTenantId(OpsUserContext.getTenantId());
        opsOrderAfsOrderItem.setAfsOrderNo(amzodOrderAfterSaleListResponse.getId());
        opsOrderAfsOrderItem.setMsku(item.getMsku());
        opsOrderAfsOrderItem.setAsin(item.getAsin());
        opsOrderAfsOrderItem.setSku(item.getLocal_sku());
        opsOrderAfsOrderItem.setOrderItemId(item.getItem_identifier());
        opsOrderAfsOrderItem.setCreateBy(OpsUserContext.getTenantId());
        opsOrderAfsOrderItem.setCreateTime(LocalDateTime.now());
        buildUpdateOrderSku(amzodOrderAfterSaleListResponse, amzodOrderAfterSaleListResponse, item, opsOrderAfsOrder, opsOrderAfsOrderItem);
    }

    @Override
    protected void buildUpdateOrderSku(AmzodOrderAfterSaleListResponse amzodOrderAfterSaleListResponse, AmzodOrderAfterSaleListResponse amzodOrderAfterSaleListResponse2, AmzodOrderAfterSaleListResponse.Item item, OpsOrderAfsOrder opsOrderAfsOrder, OpsOrderAfsOrderItem opsOrderAfsOrderItem) {
        opsOrderAfsOrderItem.setProductName(item.getItem_name());
        opsOrderAfsOrderItem.setImage(item.getSmall_image_url());
        opsOrderAfsOrderItem.setProductAmount(null);
        opsOrderAfsOrderItem.setProductAfsAmount(item.getAfter_quantity());
        opsOrderAfsOrderItem.setRefundAmount(item.getRefund_amount());
        opsOrderAfsOrderItem.setRemark(item.getAfter_reason());
        opsOrderAfsOrderItem.setUpdateBy(OpsUserContext.getTenantId());
        opsOrderAfsOrderItem.setUpdateTime(LocalDateTime.now());
    }

}
