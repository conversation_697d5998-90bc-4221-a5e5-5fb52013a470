package com.dhp.oms.ds.infrastructure.mapper;

import com.baomidou.mybatisplus.extension.toolkit.ChainWrappers;
import com.dhp.oms.framework.context.user.OpsUserContext;
import com.dhp.oms.framework.core.entity.ops.OpsSyncTask;
import com.dhp.oms.framework.core.mapper.AbstractBaseMapper;
import org.apache.ibatis.annotations.Mapper;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-10
 */
@Mapper
public interface OpsSyncTaskMapper extends AbstractBaseMapper<OpsSyncTask> {

    default OpsSyncTask getByTenantIdAndCode(String code) {
        return ChainWrappers.lambdaQueryChain(OpsSyncTask.class)
                .eq(OpsSyncTask::getTenantId, OpsUserContext.getTenantId())
                .eq(OpsSyncTask::getCode, code)
                .one();
    }

}
