package com.dhp.oms.ds.infrastructure.service.impl;

import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson2.JSON;
import com.dhp.oms.ds.constant.CommonConstants;
import com.dhp.oms.ds.infrastructure.converter.ProductSkuApiConverter;
import com.dhp.oms.ds.infrastructure.converter.ProductSpuApiConverter;
import com.dhp.oms.ds.infrastructure.mapper.CategoryMapper;
import com.dhp.oms.ds.infrastructure.mapper.OpsTenantMapper;
import com.dhp.oms.ds.infrastructure.mapper.sku.OpsProductSkuBaseInfoMapper;
import com.dhp.oms.ds.infrastructure.mapper.sku.OpsProductSkuClearanceFeeMapper;
import com.dhp.oms.ds.infrastructure.mapper.sku.OpsProductSkuDeclarationMapper;
import com.dhp.oms.ds.infrastructure.mapper.sku.OpsProductSkuPurchaseInfoMapper;
import com.dhp.oms.ds.infrastructure.mapper.sku.OpsProductSkuPurchaseItemMapper;
import com.dhp.oms.ds.infrastructure.mapper.sku.OpsProductSkuSpecificationInfoMapper;
import com.dhp.oms.ds.infrastructure.mapper.sku.OpsProductSkuSpecificationSingleMapper;
import com.dhp.oms.ds.infrastructure.mapper.spu.ProductSpuBaseInfoMapper;
import com.dhp.oms.ds.infrastructure.mapper.spu.ProductSpuClearanceFeeMapper;
import com.dhp.oms.ds.infrastructure.mapper.spu.ProductSpuDeclarationMapper;
import com.dhp.oms.ds.infrastructure.mapper.spu.ProductSpuPurchaseInfoMapper;
import com.dhp.oms.ds.infrastructure.mapper.spu.ProductSpuSpecificationInfoMapper;
import com.dhp.oms.ds.infrastructure.mapper.store.StoreInfoMapper;
import com.dhp.oms.ds.infrastructure.service.CronService;
import com.dhp.oms.ds.infrastructure.service.OrderSyncService;
import com.dhp.oms.framework.context.user.OpsUserContext;
import com.dhp.oms.framework.core.entity.ops.OpsTenant;
import com.dhp.oms.framework.core.entity.ops.product.OpsProductCategory;
import com.dhp.oms.framework.core.entity.ops.sku.OpsProductSkuBaseInfo;
import com.dhp.oms.framework.core.entity.ops.sku.OpsProductSkuClearanceFee;
import com.dhp.oms.framework.core.entity.ops.sku.OpsProductSkuDeclaration;
import com.dhp.oms.framework.core.entity.ops.sku.OpsProductSkuPurchaseInfo;
import com.dhp.oms.framework.core.entity.ops.sku.OpsProductSkuPurchaseItem;
import com.dhp.oms.framework.core.entity.ops.sku.OpsProductSkuSpecificationInfo;
import com.dhp.oms.framework.core.entity.ops.sku.OpsProductSkuSpecificationSingle;
import com.dhp.oms.framework.core.entity.ops.spu.ProductSpuBaseInfo;
import com.dhp.oms.framework.core.entity.ops.spu.ProductSpuClearanceFee;
import com.dhp.oms.framework.core.entity.ops.spu.ProductSpuDeclaration;
import com.dhp.oms.framework.core.entity.ops.spu.ProductSpuPurchaseInfo;
import com.dhp.oms.framework.core.entity.ops.spu.ProductSpuSpecificationInfo;
import com.dhp.oms.framework.core.entity.ops.store.StoreInfo;
import com.dhp.oms.framework.utils.CommonUtils;
import com.dhp.oms.framework.utils.token.payload.OpsUserPayload;
import com.dhp.oms.third.api.common.ExternApiResult;
import com.dhp.oms.third.api.module.base.LingXingApiContext;
import com.dhp.oms.third.api.module.afs.request.AmzodOrderAfterSaleListRequest;
import com.dhp.oms.third.api.module.afs.request.DataMwsReportRefundOrdersRequest;
import com.dhp.oms.third.api.module.afs.request.DataOrderFbaExchangeOrderListRequest;
import com.dhp.oms.third.api.module.afs.request.MpReturnsWorkOrderListRequest;
import com.dhp.oms.third.api.module.afs.AfsApi;
import com.dhp.oms.third.api.module.sku.ProductSkuApi;
import com.dhp.oms.third.api.module.sku.request.BatchGetProductInfoRequest;
import com.dhp.oms.third.api.module.sku.request.GetSkuListRequest;
import com.dhp.oms.third.api.module.sku.response.ProductSkuDetailResponse;
import com.dhp.oms.third.api.module.sku.response.ProductSkuResponse;
import com.dhp.oms.third.api.module.spu.ProductSpuApi;
import com.dhp.oms.third.api.module.spu.request.GetSpuInfoRequest;
import com.dhp.oms.third.api.module.spu.request.GetSpuListRequest;
import com.dhp.oms.third.api.module.spu.request.ProductSpuDetailResponse;
import com.dhp.oms.third.api.module.spu.response.ProductSpuResponse;
import com.google.common.collect.Lists;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.tuple.ImmutableTriple;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class CronServiceImpl implements CronService {

    @Resource
    private ProductSpuApi productSpuApi;

    @Resource
    private ProductSkuApi productSkuApi;

    @Resource
    private AfsApi afsApi;

    @Resource
    private OpsTenantMapper opsTenantMapper;

    @Resource
    private ProductSpuBaseInfoMapper productSpuBaseInfoMapper;

    @Resource
    private ProductSpuClearanceFeeMapper productSpuClearanceFeeMapper;

    @Resource
    private ProductSpuDeclarationMapper productSpuDeclarationMapper;

    @Resource
    private ProductSpuPurchaseInfoMapper productSpuPurchaseInfoMapper;

    @Resource
    private ProductSpuSpecificationInfoMapper productSpuSpecificationInfoMapper;

    @Resource
    private OpsProductSkuBaseInfoMapper opsProductSkuBaseInfoMapper;

    @Resource
    private OpsProductSkuClearanceFeeMapper opsProductSkuClearanceFeeMapper;

    @Resource
    private OpsProductSkuDeclarationMapper opsProductSkuDeclarationMapper;

    @Resource
    private OpsProductSkuPurchaseInfoMapper opsProductSkuPurchaseInfoMapper;

    @Resource
    private OpsProductSkuPurchaseItemMapper opsProductSkuPurchaseItemMapper;

    @Resource
    private OpsProductSkuSpecificationInfoMapper opsProductSkuSpecificationInfoMapper;

    @Resource
    private OpsProductSkuSpecificationSingleMapper opsProductSkuSpecificationSingleMapper;

    @Resource
    private CategoryMapper categoryMapper;

    @Resource
    private StoreInfoMapper storeInfoMapper;

    @Resource
    private ThreadPoolTaskExecutor syncProductSkuExecutor;

    @Resource
    private ThreadPoolTaskExecutor syncProductSkuDetailExecutor;

    @Override
    public void syncProductSpu() {
        List<OpsTenant> opsTenants = opsTenantMapper.listAll();
        log.info("syncProductSpu opsTenants size: {}", opsTenants.size());

        for (OpsTenant opsTenant : opsTenants) {
            try {
                OpsUserPayload opsUserPayload = new OpsUserPayload();
                opsUserPayload.setTenantId(opsTenant.getTenantId());
                OpsUserContext.set(opsUserPayload);
                log.info("syncProductSpu opsTenant tenantId: {}, tenantName: {} begin", opsTenant.getId(), opsTenant.getTenantName());
                LingXingApiContext.execute(opsTenant.getTenantId(), this::syncProductSpuByTenant);
                log.info("syncProductSpu opsTenant tenantId: {}, tenantName: {} end", opsTenant.getId(), opsTenant.getTenantName());
            } finally {
                OpsUserContext.remove();
            }
        }
    }

    private void syncProductSpuByTenant() {
        int retry = 0;
        GetSpuListRequest getSpuListRequest = new GetSpuListRequest();
        List<Long> notInDeleteIdList = new ArrayList<>();
        List<Long> notInDeletePsIdList = new ArrayList<>();
        List<ProductSpuResponse> inputList;
        do {
            try {
                ExternApiResult<List<ProductSpuResponse>> result = productSpuApi.getSpuList(getSpuListRequest);
                if (Objects.isNull(result) || !Objects.equals(result.getCode(), ExternApiResult.OK)) {
                    log.error("syncProductSpuByTenant get result error, result: {}", result);
//                    throw new RuntimeException(Objects.nonNull(result) ? result.getMessage() : null);
                    continue;
                }
                inputList = result.getData();
                if (CollectionUtils.isEmpty(inputList)) {
                    retry = 0;
                    break;
                }
                doSyncProductSpu(inputList, notInDeleteIdList, notInDeletePsIdList);
                getSpuListRequest.setOffset(getSpuListRequest.getOffset() + getSpuListRequest.getLength());
                retry = 0;
            } catch (Exception e) {
                log.error("syncProductSpuByTenant error: ", e);
                retry++;
            }
        } while (retry < 3);
        log.info("syncProductSpuByTenant notInDeleteIdList[{}] retry[{}]", notInDeleteIdList.size(), retry);

        // 是否完整数据，如果不是完整数据，只新增/更新，不标记删除
        // 是完整数据，标记删除
        if (retry > 0) {
            return;
        }
        if (CollectionUtils.isNotEmpty(notInDeleteIdList)) {
            productSpuBaseInfoMapper.deleteByNotIdList(notInDeleteIdList);
        }
        if (CollectionUtils.isNotEmpty(notInDeletePsIdList)) {
            productSpuClearanceFeeMapper.deleteByNotIdList(notInDeletePsIdList);
        }
        if (CollectionUtils.isNotEmpty(notInDeletePsIdList)) {
            productSpuDeclarationMapper.deleteByNotIdList(notInDeletePsIdList);
        }
        if (CollectionUtils.isNotEmpty(notInDeletePsIdList)) {
            productSpuPurchaseInfoMapper.deleteByNotIdList(notInDeletePsIdList);
        }
        if (CollectionUtils.isNotEmpty(notInDeletePsIdList)) {
            productSpuSpecificationInfoMapper.deleteByNotIdList(notInDeletePsIdList);
        }
    }

    private void doSyncProductSpu(List<ProductSpuResponse> inputList, List<Long> notInDeleteIdList, List<Long> notInDeletePsIdList) {
        // 获取详情数据，调用10次
        Map<Long, ProductSpuDetailResponse> detailEntryMap = inputList.stream()
                .map(item -> {
                    GetSpuInfoRequest getSpuInfoRequest = new GetSpuInfoRequest();
                    getSpuInfoRequest.setPs_id(item.getPs_id());
                    return productSpuApi.getSpuInfo(getSpuInfoRequest);
                })
                .filter(Objects::nonNull)
                .map(ExternApiResult::getData)
                .collect(Collectors.toMap(
                        ProductSpuDetailResponse::getPs_id,
                        Function.identity(),
                        (pre, next) -> next));

        // 分类
        List<Long> cidList = inputList.stream()
                .map(ProductSpuResponse::getCid)
                .distinct()
                .collect(Collectors.toList());
        Map<Long, OpsProductCategory> opsProductCategoryMap = CollectionUtils.isNotEmpty(cidList) ?
                categoryMapper.listByCidList(cidList)
                        .stream()
                        .collect(Collectors.toMap(
                                OpsProductCategory::getCid,
                                Function.identity(),
                                (pre, next) -> next)) :
                Collections.emptyMap();

        // 查询DB数据
        List<Long> psIdList = inputList.stream().map(ProductSpuResponse::getPs_id).distinct().toList();
        List<ProductSpuBaseInfo> dbProductSpuBaseInfoList = productSpuBaseInfoMapper.listByPsIdList(psIdList);

        // 记录全部数据库ID
        for (ProductSpuBaseInfo productSpuBaseInfo : dbProductSpuBaseInfoList) {
            notInDeleteIdList.add(productSpuBaseInfo.getId());
            notInDeletePsIdList.add(productSpuBaseInfo.getPsId());
        }

        // 对比新增和更新
        ImmutableTriple<List<ProductSpuResponse>, List<ProductSpuResponse>, List<Long>> result = CommonUtils.compare(
                inputList,
                dbProductSpuBaseInfoList,
                item -> String.valueOf(item.getPs_id()),
                item -> String.valueOf(item.getPsId()),
                ProductSpuBaseInfo::getId,
                ProductSpuResponse::setBizId);
        List<ProductSpuResponse> insertList = result.getLeft();
        List<ProductSpuResponse> updateList = result.getMiddle();

        // 更新
        List<ProductSpuBaseInfo> productSpuBaseInfoUpdateList = new ArrayList<>();
        List<ProductSpuClearanceFee> productSpuClearanceFeeUpdateList = new ArrayList<>();
        List<ProductSpuDeclaration> productSpuDeclarationUpdateList = new ArrayList<>();
        List<ProductSpuPurchaseInfo> productSpuPurchaseInfoUpdateList = new ArrayList<>();
        List<ProductSpuSpecificationInfo> productSpuSpecificationInfoUpdateList = new ArrayList<>();
        for (ProductSpuResponse productSpuResponse : updateList) {
            ProductSpuDetailResponse productSpuDetailResponse = detailEntryMap.get(productSpuResponse.getPs_id());
            if (Objects.isNull(productSpuDetailResponse)) {
                continue;
            }

            // SPU基础数据
            ProductSpuBaseInfo baseInfo = new ProductSpuBaseInfo();
            baseInfo.setId(productSpuResponse.getBizId());
            ProductSpuApiConverter.INSTANCE.buildProductSpuBaseInfoUpdate(productSpuResponse, productSpuDetailResponse, opsProductCategoryMap, baseInfo, productSpuBaseInfoUpdateList);

            // SPU清关数据
            ProductSpuDetailResponse.Clearance clearanceEntry = Optional.ofNullable(productSpuDetailResponse.getLogistics())
                    .map(ProductSpuDetailResponse.Logistics::getClearance)
                    .orElse(null);
            ProductSpuClearanceFee productSpuClearanceFee = new ProductSpuClearanceFee();
            productSpuClearanceFee.setId(baseInfo.getId());
            ProductSpuApiConverter.INSTANCE.buildProductSpuClearanceFeeUpdate(productSpuResponse, clearanceEntry, productSpuClearanceFee, productSpuClearanceFeeUpdateList);

            // SPU报关数据
            ProductSpuDetailResponse.Declaration declarationEntry = Optional.ofNullable(productSpuDetailResponse.getLogistics())
                    .map(ProductSpuDetailResponse.Logistics::getDeclaration)
                    .orElse(null);
            ProductSpuDeclaration productSpuDeclaration = new ProductSpuDeclaration();
            productSpuDeclaration.setId(baseInfo.getId());
            ProductSpuApiConverter.INSTANCE.buildProductSpuDeclarationUpdate(productSpuResponse, declarationEntry, productSpuDeclaration, productSpuDeclarationUpdateList);

            // SPU采购数据
            ProductSpuPurchaseInfo productSpuPurchaseInfo = new ProductSpuPurchaseInfo();
            productSpuDeclaration.setId(baseInfo.getId());
            ProductSpuApiConverter.INSTANCE.buildProductSpuPurchaseInfoUpdate(productSpuResponse, productSpuDetailResponse.getPurchase_info(), productSpuPurchaseInfo, productSpuPurchaseInfoUpdateList);

            // SPU规格数据
            ProductSpuSpecificationInfo spuSpecificationInfo = new ProductSpuSpecificationInfo();
            spuSpecificationInfo.setId(baseInfo.getId());
            ProductSpuApiConverter.INSTANCE.buildProductSpuSpecificationInfoUpdate(productSpuResponse, productSpuDetailResponse.getPurchase_info(), spuSpecificationInfo, productSpuSpecificationInfoUpdateList);
        }
        if (CollectionUtils.isNotEmpty(productSpuBaseInfoUpdateList)) {
            productSpuBaseInfoMapper.updateBatchById(productSpuBaseInfoUpdateList);
        }
        if (CollectionUtils.isNotEmpty(productSpuClearanceFeeUpdateList)) {
            productSpuClearanceFeeMapper.updateBatchById(productSpuClearanceFeeUpdateList);
        }
        if (CollectionUtils.isNotEmpty(productSpuDeclarationUpdateList)) {
            productSpuDeclarationMapper.updateBatchById(productSpuDeclarationUpdateList);
        }
        if (CollectionUtils.isNotEmpty(productSpuPurchaseInfoUpdateList)) {
            productSpuPurchaseInfoMapper.updateBatchById(productSpuPurchaseInfoUpdateList);
        }
        if (CollectionUtils.isNotEmpty(productSpuSpecificationInfoUpdateList)) {
            productSpuSpecificationInfoMapper.updateBatchById(productSpuSpecificationInfoUpdateList);
        }
        log.info("doSyncProductSpu productSpuBaseInfoUpdateList: {}, productSpuClearanceFeeUpdateList: {}, productSpuDeclarationUpdateList: {}",
                productSpuBaseInfoUpdateList.size(), productSpuClearanceFeeUpdateList.size(), productSpuDeclarationUpdateList.size());

        // 新增
        List<ProductSpuBaseInfo> productSpuBaseInfoInsertList = new ArrayList<>();
        List<ProductSpuClearanceFee> productSpuClearanceFeeInsertList = new ArrayList<>();
        List<ProductSpuDeclaration> productSpuDeclarationInsertList = new ArrayList<>();
        List<ProductSpuPurchaseInfo> productSpuPurchaseInfoInsertList = new ArrayList<>();
        List<ProductSpuSpecificationInfo> productSpuSpecificationInfoInsertList = new ArrayList<>();
        for (ProductSpuResponse productSpuResponse : insertList) {
            ProductSpuDetailResponse productSpuDetailResponse = detailEntryMap.get(productSpuResponse.getPs_id());
            if (Objects.isNull(productSpuDetailResponse)) {
                continue;
            }

            // SPU基础数据
            ProductSpuBaseInfo baseInfo = new ProductSpuBaseInfo();
            baseInfo.setId(productSpuResponse.getBizId());
            ProductSpuApiConverter.INSTANCE.buildProductSpuBaseInfoInsert(productSpuResponse, productSpuDetailResponse, opsProductCategoryMap, baseInfo, productSpuBaseInfoInsertList);

            // SPU清关数据
            ProductSpuDetailResponse.Clearance clearanceEntry = Optional.ofNullable(productSpuDetailResponse.getLogistics())
                    .map(ProductSpuDetailResponse.Logistics::getClearance)
                    .orElse(null);
            ProductSpuApiConverter.INSTANCE.buildProductSpuClearanceFeeInsert(productSpuResponse, clearanceEntry, productSpuClearanceFeeInsertList);

            // SPU报关数据
            ProductSpuDetailResponse.Declaration declarationEntry = Optional.ofNullable(productSpuDetailResponse.getLogistics())
                    .map(ProductSpuDetailResponse.Logistics::getDeclaration)
                    .orElse(null);
            ProductSpuApiConverter.INSTANCE.buildOpsProductSpuDeclarationInsert(productSpuResponse, declarationEntry, productSpuDeclarationInsertList);

            // SPU采购数据
            ProductSpuApiConverter.INSTANCE.buildProductSpuPurchaseInfoInsert(productSpuResponse, productSpuDetailResponse.getPurchase_info(), productSpuPurchaseInfoInsertList);

            // SPU规格数据
            ProductSpuApiConverter.INSTANCE.buildProductSpuSpecificationInfoInsert(productSpuResponse, productSpuDetailResponse.getPurchase_info(), productSpuSpecificationInfoInsertList);
        }
        if (CollectionUtils.isNotEmpty(productSpuBaseInfoInsertList)) {
            productSpuBaseInfoMapper.saveBatch(productSpuBaseInfoInsertList);
        }
        if (CollectionUtils.isNotEmpty(productSpuClearanceFeeInsertList)) {
            productSpuClearanceFeeMapper.saveBatch(productSpuClearanceFeeInsertList);
        }
        if (CollectionUtils.isNotEmpty(productSpuDeclarationInsertList)) {
            productSpuDeclarationMapper.saveBatch(productSpuDeclarationInsertList);
        }
        if (CollectionUtils.isNotEmpty(productSpuPurchaseInfoInsertList)) {
            productSpuPurchaseInfoMapper.saveBatch(productSpuPurchaseInfoInsertList);
        }
        if (CollectionUtils.isNotEmpty(productSpuSpecificationInfoInsertList)) {
            productSpuSpecificationInfoMapper.saveBatch(productSpuSpecificationInfoInsertList);
        }

        log.info("doSyncProductSpu productSpuBaseInfoInsertList: {}, productSpuClearanceFeeInsertList: {}, productSpuDeclarationInsertList: {}",
                productSpuBaseInfoInsertList.size(), productSpuClearanceFeeInsertList.size(), productSpuDeclarationInsertList.size());
    }

    @Override
    public void syncProductSku() {
        List<OpsTenant> opsTenants = opsTenantMapper.listAll();
        log.info("syncProductSku opsTenants size: {}", opsTenants.size());

        for (OpsTenant opsTenant : opsTenants) {
            try {
                OpsUserPayload opsUserPayload = new OpsUserPayload();
                opsUserPayload.setTenantId(opsTenant.getTenantId());
                OpsUserContext.set(opsUserPayload);
                log.info("syncProductSku opsTenant tenantId: {}, tenantName: {} begin", opsTenant.getId(), opsTenant.getTenantName());
                LingXingApiContext.execute(opsTenant.getTenantId(), this::syncProductSkuByTenant);
                log.info("syncProductSku opsTenant tenantId: {}, tenantName: {} end", opsTenant.getId(), opsTenant.getTenantName());
            } finally {
                OpsUserContext.remove();
            }
        }
    }

    private void syncProductSkuByTenant() {
        GetSkuListRequest getSkuListRequest = new GetSkuListRequest();
        int retry = 0;
        List<Long> notInDeleteIdList = new ArrayList<>();
        List<String> notInDeleteSkuIdList = new ArrayList<>();
        List<ProductSkuResponse> inputList;
        do {
            try {
                ExternApiResult<List<ProductSkuResponse>> result = productSkuApi.productList(getSkuListRequest);
                if (Objects.isNull(result) || !Objects.equals(result.getCode(), ExternApiResult.OK)) {
                    log.error("syncProductSkuByTenant error  result: {}", JSON.toJSONString(result));
                    continue;
//                    throw new RuntimeException(Objects.nonNull(result) ? result.getMessage() : null);
                }
                inputList = result.getData();
                if (CollectionUtils.isEmpty(inputList)) {
                    retry = 0;
                    break;
                }
                doSyncProductSku(inputList, notInDeleteIdList, notInDeleteSkuIdList);
                getSkuListRequest.setOffset(getSkuListRequest.getOffset() + getSkuListRequest.getLength());
                retry = 0;
            } catch (Exception e) {
                log.error("syncProductSkuByTenant error: ", e);
                retry++;
            }
        } while (retry < 3);
        log.info("syncProductSkuByTenant notInDeleteIdList[{}] retry[{}]", notInDeleteIdList.size(), retry);

        // todo 后面增加一个task表记录同步时间，落后于同步时间的标记为删除
        // 是否完整数据，如果不是完整数据，只新增/更新，不标记删除
        // 是完整数据，标记删除
        if (retry > 0) {
            return;
        }
        if (CollectionUtils.isNotEmpty(notInDeleteIdList)) {
            opsProductSkuBaseInfoMapper.deleteByNotIdList(notInDeleteIdList);
        }
        if (CollectionUtils.isNotEmpty(notInDeleteSkuIdList)) {
            opsProductSkuClearanceFeeMapper.deleteByNotIdList(notInDeleteSkuIdList);
        }
        if (CollectionUtils.isNotEmpty(notInDeleteSkuIdList)) {
            opsProductSkuDeclarationMapper.deleteByNotIdList(notInDeleteSkuIdList);
        }
        if (CollectionUtils.isNotEmpty(notInDeleteSkuIdList)) {
            opsProductSkuPurchaseInfoMapper.deleteByNotIdList(notInDeleteSkuIdList);
        }
        if (CollectionUtils.isNotEmpty(notInDeleteSkuIdList)) {
            opsProductSkuPurchaseItemMapper.deleteByNotIdList(notInDeleteSkuIdList);
        }
        if (CollectionUtils.isNotEmpty(notInDeleteSkuIdList)) {
            opsProductSkuSpecificationInfoMapper.deleteByNotIdList(notInDeleteSkuIdList);
        }
        if (CollectionUtils.isNotEmpty(notInDeleteSkuIdList)) {
            opsProductSkuSpecificationSingleMapper.deleteByNotIdList(notInDeleteSkuIdList);
        }
    }

    private void doSyncProductSku(List<ProductSkuResponse> inputList, List<Long> notInDeleteIdList, List<String> notInDeleteSkuIdList) {
        // 获取详情数据，调用10次
        List<CompletableFuture<ExternApiResult<List<ProductSkuDetailResponse>>>> futureList = new ArrayList<>();
        for (List<ProductSkuResponse> item : Lists.partition(inputList, 100)) {
            futureList.add(CompletableFuture.supplyAsync(() -> {
                List<Long> productIds = item.stream().map(ProductSkuResponse::getId).collect(Collectors.toList());
                BatchGetProductInfoRequest batchGetProductInfoRequest = new BatchGetProductInfoRequest();
                batchGetProductInfoRequest.setProductIds(productIds);
//                ExternApiResult<Object> objectExternApiResult = productSkuApi.batchGetProductInfo2(batchGetProductInfoRequest);
//                log.info("batchGetProductInfo2 result: {}", JSON.toJSONString(objectExternApiResult));
                return productSkuApi.batchGetProductInfo(batchGetProductInfoRequest);
            }, syncProductSkuDetailExecutor));
        }

        Map<Long, ProductSkuDetailResponse> detailEntryMap = futureList
                .stream()
                .map(CompletableFuture::join)
                .map(ExternApiResult::getData)
                .filter(Objects::nonNull)
                .flatMap(Collection::stream)
                .collect(Collectors.toMap(
                        ProductSkuDetailResponse::getId,
                        Function.identity(),
                        (pre, next) -> next));

        // 分类
        List<Long> cidList = inputList.stream()
                .map(ProductSkuResponse::getCid)
                .distinct()
                .collect(Collectors.toList());
        Map<Long, OpsProductCategory> opsProductCategoryMap = CollectionUtils.isNotEmpty(cidList) ?
                categoryMapper.listByCidList(cidList)
                        .stream()
                        .collect(Collectors.toMap(
                                OpsProductCategory::getCid,
                                Function.identity(),
                                (pre, next) -> next)) :
                Collections.emptyMap();

        // 查询DB数据（交集）
        List<String> skuList = inputList.stream().map(ProductSkuResponse::getSku).distinct().toList();
        List<OpsProductSkuBaseInfo> dbSkuBaseInfoList = opsProductSkuBaseInfoMapper.listBySkuList(skuList);

        // 记录全部数据库ID，不在交集内的为已删除的
        for (OpsProductSkuBaseInfo opsProductSkuBaseInfo : dbSkuBaseInfoList) {
            notInDeleteIdList.add(opsProductSkuBaseInfo.getId());
            notInDeleteSkuIdList.add(opsProductSkuBaseInfo.getSkuId());
        }

        // 对比新增和更新
        ImmutableTriple<List<ProductSkuResponse>, List<ProductSkuResponse>, List<Long>> result = CommonUtils.compare(
                inputList,
                dbSkuBaseInfoList,
                ProductSkuResponse::getSku,
                OpsProductSkuBaseInfo::getSku,
                OpsProductSkuBaseInfo::getId,
                ProductSkuResponse::setBizId);
        List<ProductSkuResponse> insertList = result.getLeft();
        List<ProductSkuResponse> updateList = result.getMiddle();

        // 更新
        List<OpsProductSkuBaseInfo> opsProductSkuBaseInfoUpdateList = new ArrayList<>();
        List<OpsProductSkuClearanceFee> opsProductSkuClearanceFeeUpdateList = new ArrayList<>();
        List<OpsProductSkuDeclaration> opsProductSkuDeclarationUpdateList = new ArrayList<>();
        List<OpsProductSkuPurchaseInfo> opsProductSkuPurchaseInfoUpdateList = new ArrayList<>();
        List<OpsProductSkuPurchaseItem> opsProductSkuPurchaseItemUpdateList = new ArrayList<>();
        List<OpsProductSkuSpecificationInfo> opsProductSkuSpecificationInfoUpdateList = new ArrayList<>();
        List<OpsProductSkuSpecificationSingle> opsProductSkuSpecificationSingleUpdateList = new ArrayList<>();
        for (ProductSkuResponse skuEntry : updateList) {
            ProductSkuDetailResponse skuDetailEntry = detailEntryMap.get(skuEntry.getId());
            if (Objects.isNull(skuDetailEntry)) {
                continue;
            }

            // SKU基础数据
            OpsProductSkuBaseInfo baseInfo = new OpsProductSkuBaseInfo();
            baseInfo.setId(skuEntry.getBizId());
            ProductSkuApiConverter.INSTANCE.buildOpsProductSkuBaseInfoUpdate(skuEntry, skuDetailEntry, opsProductCategoryMap, baseInfo, opsProductSkuBaseInfoUpdateList);

            // SKU清关数据
            OpsProductSkuClearanceFee opsProductSkuClearanceFee = new OpsProductSkuClearanceFee();
            opsProductSkuClearanceFee.setId(baseInfo.getId());
            ProductSkuApiConverter.INSTANCE.buildOpsProductSkuClearanceFeeUpdate(skuEntry, skuDetailEntry.getClearance(), opsProductSkuClearanceFee, opsProductSkuClearanceFeeUpdateList);

            // SKU报关数据
            OpsProductSkuDeclaration opsProductSkuDeclaration = new OpsProductSkuDeclaration();
            opsProductSkuDeclaration.setId(baseInfo.getId());
            ProductSkuApiConverter.INSTANCE.buildOpsProductSkuDeclarationUpdate(skuEntry, skuDetailEntry.getDeclaration(), opsProductSkuDeclaration, opsProductSkuDeclarationUpdateList);

            // SKU采购数据info
            OpsProductSkuPurchaseInfo opsProductSkuPurchaseInfo = new OpsProductSkuPurchaseInfo();
            opsProductSkuPurchaseInfo.setId(baseInfo.getId());
            ProductSkuApiConverter.INSTANCE.buildOpsProductSkuPurchaseInfoUpdate(skuEntry, skuDetailEntry, opsProductSkuPurchaseInfo, opsProductSkuPurchaseInfoUpdateList);

            // SKU采购数据item
            OpsProductSkuPurchaseItem opsProductSkuPurchaseItem = new OpsProductSkuPurchaseItem();
            opsProductSkuPurchaseInfo.setId(baseInfo.getId());
            ProductSkuApiConverter.INSTANCE.buildOpsProductSkuPurchaseItemUpdate(skuEntry, skuDetailEntry, opsProductSkuPurchaseItem, opsProductSkuPurchaseItemUpdateList);

            // SKU规格数据info sku:SKU规格数据info = 1:N
            OpsProductSkuSpecificationInfo opsProductSkuSpecificationInfo = new OpsProductSkuSpecificationInfo();
            opsProductSkuSpecificationInfo.setId(baseInfo.getId());
            ProductSkuApiConverter.INSTANCE.buildOpsProductSkuSpecificationInfoUpdate(skuEntry, skuDetailEntry, opsProductSkuSpecificationInfo, opsProductSkuSpecificationInfoUpdateList);

            // SKU规格数据single
            OpsProductSkuSpecificationSingle opsProductSkuSpecificationSingle = new OpsProductSkuSpecificationSingle();
            opsProductSkuSpecificationSingle.setId(baseInfo.getId());
            ProductSkuApiConverter.INSTANCE.buildOpsProductSkuSpecificationSingleUpdate(skuEntry, skuDetailEntry, opsProductSkuSpecificationSingle, opsProductSkuSpecificationSingleUpdateList);
        }
        if (CollectionUtils.isNotEmpty(opsProductSkuBaseInfoUpdateList)) {
            opsProductSkuBaseInfoMapper.updateBatchById(opsProductSkuBaseInfoUpdateList);
        }
        if (CollectionUtils.isNotEmpty(opsProductSkuClearanceFeeUpdateList)) {
            opsProductSkuClearanceFeeMapper.updateBatchById(opsProductSkuClearanceFeeUpdateList);
        }
        if (CollectionUtils.isNotEmpty(opsProductSkuDeclarationUpdateList)) {
            opsProductSkuDeclarationMapper.updateBatchById(opsProductSkuDeclarationUpdateList);
        }
        if (CollectionUtils.isNotEmpty(opsProductSkuPurchaseInfoUpdateList)) {
            opsProductSkuPurchaseInfoMapper.updateBatchById(opsProductSkuPurchaseInfoUpdateList);
        }
        if (CollectionUtils.isNotEmpty(opsProductSkuPurchaseItemUpdateList)) {
            opsProductSkuPurchaseItemMapper.updateBatchById(opsProductSkuPurchaseItemUpdateList);
        }
        if (CollectionUtils.isNotEmpty(opsProductSkuSpecificationInfoUpdateList)) {
            opsProductSkuSpecificationInfoMapper.updateBatchById(opsProductSkuSpecificationInfoUpdateList);
        }
        if (CollectionUtils.isNotEmpty(opsProductSkuSpecificationSingleUpdateList)) {
            opsProductSkuSpecificationSingleMapper.updateBatchById(opsProductSkuSpecificationSingleUpdateList);
        }
        log.info("doSyncProductSku opsProductSkuBaseInfoUpdateList: {}, opsProductSkuClearanceFeeUpdateList: {}, opsProductSkuDeclarationUpdateList: {}",
                opsProductSkuBaseInfoUpdateList.size(), opsProductSkuClearanceFeeUpdateList.size(), opsProductSkuDeclarationUpdateList.size());

        // 新增
        List<OpsProductSkuBaseInfo> opsProductSkuBaseInfoInsertList = new ArrayList<>();
        List<OpsProductSkuClearanceFee> opsProductSkuClearanceFeeInsertList = new ArrayList<>();
        List<OpsProductSkuDeclaration> opsProductSkuDeclarationInsertList = new ArrayList<>();
        List<OpsProductSkuPurchaseInfo> opsProductSkuPurchaseInfoInsertList = new ArrayList<>();
        List<OpsProductSkuPurchaseItem> opsProductSkuPurchaseItemInsertList = new ArrayList<>();
        List<OpsProductSkuSpecificationInfo> opsProductSkuSpecificationInfoInsertList = new ArrayList<>();
        List<OpsProductSkuSpecificationSingle> opsProductSkuSpecificationSingleInsertList = new ArrayList<>();
        for (ProductSkuResponse skuEntry : insertList) {
            ProductSkuDetailResponse skuDetailEntry = detailEntryMap.get(skuEntry.getId());
            if (Objects.isNull(skuDetailEntry)) {
                continue;
            }

            // SKU基础数据
            OpsProductSkuBaseInfo baseInfo = new OpsProductSkuBaseInfo();
            ProductSkuApiConverter.INSTANCE.buildOpsProductSkuBaseInfoInsert(skuEntry, skuDetailEntry, opsProductCategoryMap, baseInfo, opsProductSkuBaseInfoInsertList);

            // SKU清关数据
            ProductSkuDetailResponse.Clearance clearanceEntry = skuDetailEntry.getClearance();
            ProductSkuApiConverter.INSTANCE.buildOpsProductSkuClearanceFeeInsert(skuEntry, clearanceEntry, opsProductSkuClearanceFeeInsertList);

            // SKU报关数据
            ProductSkuDetailResponse.Declaration declarationEntry = skuDetailEntry.getDeclaration();
            ProductSkuApiConverter.INSTANCE.buildOpsProductSkuDeclarationInsert(skuEntry, declarationEntry, opsProductSkuDeclarationInsertList);

            // SKU采购数据info
            ProductSkuApiConverter.INSTANCE.buildOpsProductSkuPurchaseInfoInsert(skuEntry, skuDetailEntry, opsProductSkuPurchaseInfoInsertList);

            // SKU采购数据item
            ProductSkuApiConverter.INSTANCE.buildOpsProductSkuPurchaseItemInsert(skuEntry, skuDetailEntry, opsProductSkuPurchaseItemInsertList);

            // SKU规格数据info
            ProductSkuApiConverter.INSTANCE.buildOpsProductSkuSpecificationInfoInsert(skuEntry, skuDetailEntry, opsProductSkuSpecificationInfoInsertList);

            // SKU规格数据single
            ProductSkuApiConverter.INSTANCE.buildOpsProductSkuSpecificationSingleInsert(skuEntry, skuDetailEntry, opsProductSkuSpecificationSingleInsertList);
        }

        if (CollectionUtils.isNotEmpty(opsProductSkuBaseInfoInsertList)) {
            opsProductSkuBaseInfoMapper.saveBatch(opsProductSkuBaseInfoInsertList);
        }
        if (CollectionUtils.isNotEmpty(opsProductSkuClearanceFeeInsertList)) {
            opsProductSkuClearanceFeeMapper.saveBatch(opsProductSkuClearanceFeeInsertList);
        }
        if (CollectionUtils.isNotEmpty(opsProductSkuDeclarationInsertList)) {
            opsProductSkuDeclarationMapper.saveBatch(opsProductSkuDeclarationInsertList);
        }
        if (CollectionUtils.isNotEmpty(opsProductSkuPurchaseInfoInsertList)) {
            opsProductSkuPurchaseInfoMapper.saveBatch(opsProductSkuPurchaseInfoInsertList);
        }
        if (CollectionUtils.isNotEmpty(opsProductSkuPurchaseItemInsertList)) {
            opsProductSkuPurchaseItemMapper.saveBatch(opsProductSkuPurchaseItemInsertList);
        }
        if (CollectionUtils.isNotEmpty(opsProductSkuSpecificationInfoInsertList)) {
            opsProductSkuSpecificationInfoMapper.saveBatch(opsProductSkuSpecificationInfoInsertList);
        }
        if (CollectionUtils.isNotEmpty(opsProductSkuSpecificationSingleInsertList)) {
            opsProductSkuSpecificationSingleMapper.saveBatch(opsProductSkuSpecificationSingleInsertList);
        }
        log.info("doSyncProductSku opsProductSkuBaseInfoInsertList: {}, opsProductSkuClearanceFeeInsertList: {}, opsProductSkuDeclarationInsertList: {}",
                opsProductSkuBaseInfoInsertList.size(), opsProductSkuClearanceFeeInsertList.size(), opsProductSkuDeclarationInsertList.size());
    }

    @Override
    public void syncOrderFull(String service) {
        syncOrderCore(
                service,
                CommonConstants.MP_ORDER_SYNC_SERVICE_IMPL.equals(service) ? LocalDateTime.now().minusMonths(1) : LocalDateTime.now().minusYears(2),
                LocalDateTime.now());
    }

    @Override
    public void syncOrderIncr(String service) {
        syncOrderCore(service, LocalDateTime.now().minusMinutes(30), LocalDateTime.now());
    }

    private void syncOrderCore(String service, LocalDateTime beginTime, LocalDateTime endTime) {
        List<OpsTenant> opsTenants = opsTenantMapper.listAll();
        log.info("syncOrderCore opsTenants size: {}", opsTenants.size());

        OrderSyncService orderSyncService = SpringUtil.getBean(service, OrderSyncService.class);
        if (Objects.isNull(orderSyncService)) {
            log.info("syncOrderCore orderSyncService is null");
            return;
        }

        for (OpsTenant opsTenant : opsTenants) {
            try {
                OpsUserPayload opsUserPayload = new OpsUserPayload();
                opsUserPayload.setTenantId(opsTenant.getTenantId());
                OpsUserContext.set(opsUserPayload);

                List<StoreInfo> storeInfoList = storeInfoMapper.listByTenantId(OpsUserContext.getTenantId());
                List<List<String>> sidList = orderSyncService.partitionSidList(storeInfoList);

                log.info("syncOrderCore opsTenant tenantId: {}, tenantName: {} begin", opsTenant.getId(), opsTenant.getTenantName());
                LingXingApiContext.execute(opsTenant.getTenantId(), () -> {
                    for (List<String> partitionSidList : sidList) {
                        orderSyncService.syncOrderByTenant(partitionSidList, beginTime, endTime);
                    }
                });
                log.info("syncOrderCore opsTenant tenantId: {}, tenantName: {} end", opsTenant.getId(), opsTenant.getTenantName());
            } finally {
                OpsUserContext.remove();
            }
        }
    }

}
