package com.dhp.oms.ds.infrastructure.mapper.spu;

import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import com.dhp.oms.framework.context.user.OpsUserContext;
import com.dhp.oms.framework.core.entity.ops.spu.ProductSpuPurchaseInfo;
import com.dhp.oms.framework.core.entity.ops.spu.ProductSpuSpecificationInfo;
import com.dhp.oms.framework.core.mapper.AbstractBaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <p>
 * SPU 规格信息 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-05
 */
@Mapper
public interface ProductSpuSpecificationInfoMapper extends AbstractBaseMapper<ProductSpuSpecificationInfo> {

    default Boolean deleteByNotIdList(List<Long> notInPsIdList) {
        return new LambdaUpdateChainWrapper<>(this)
                .notIn(ProductSpuSpecificationInfo::getSpuId, notInPsIdList)
                .eq(ProductSpuSpecificationInfo::getTenantId, OpsUserContext.getTenantId())
                .set(ProductSpuSpecificationInfo::getDeleted, Boolean.TRUE)
                .update();
    }

}
