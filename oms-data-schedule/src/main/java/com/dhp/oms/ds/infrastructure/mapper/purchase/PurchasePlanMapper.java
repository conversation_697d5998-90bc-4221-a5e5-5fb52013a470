package com.dhp.oms.ds.infrastructure.mapper.purchase;

import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import com.dhp.oms.framework.core.entity.ops.purchase.PurchasePlan;
import com.dhp.oms.framework.core.mapper.AbstractBaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Set;

/**
 * <p>
 * 供应商基础信息表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-25
 */
@Mapper
public interface PurchasePlanMapper extends AbstractBaseMapper<PurchasePlan> {

    default List<PurchasePlan> findByPlanSns(Long tenantId, String ppgSn, Set<String> planSns) {
        return new LambdaQueryChainWrapper<>(this)
                .eq(PurchasePlan::getTenantId, tenantId)
                .eq(PurchasePlan::getDeleted, Boolean.FALSE)
                .eq(PurchasePlan::getPpgSn, ppgSn)
                .in(PurchasePlan::getPlanSn, planSns)
                .list();
    }

    default List<PurchasePlan> findByPpgSn(Long tenantId, String ppgSn) {
        return new LambdaQueryChainWrapper<>(this)
                .eq(PurchasePlan::getTenantId, tenantId)
                .eq(PurchasePlan::getDeleted, Boolean.FALSE)
                .eq(PurchasePlan::getPpgSn, ppgSn)
                .list();
    }

    default void removeByPpgSn(Long tenantId, String ppgSn) {
        new LambdaUpdateChainWrapper<>(this)
                .eq(PurchasePlan::getTenantId, tenantId)
                .eq(PurchasePlan::getDeleted, Boolean.FALSE)
                .eq(PurchasePlan::getPpgSn, ppgSn)
                .set(PurchasePlan::getDeleted, Boolean.TRUE)
                .update();
    }

}
