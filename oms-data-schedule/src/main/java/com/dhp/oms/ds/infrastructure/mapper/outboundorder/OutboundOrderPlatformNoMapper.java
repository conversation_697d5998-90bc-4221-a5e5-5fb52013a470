package com.dhp.oms.ds.infrastructure.mapper.outboundorder;

import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import com.dhp.oms.framework.context.tenant.TenentContext;
import com.dhp.oms.framework.core.entity.ops.outboundorder.OutboundOrderPlatformNo;
import com.dhp.oms.framework.core.mapper.AbstractBaseMapper;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.ibatis.annotations.Mapper;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Set;

/**
 * <p>
 * 销售出库单明细 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-20
 */
@Mapper
public interface OutboundOrderPlatformNoMapper extends AbstractBaseMapper<OutboundOrderPlatformNo> {


    default List<OutboundOrderPlatformNo> findByWoIds(Set<String> woIds) {
        if (CollectionUtils.isEmpty(woIds)) {
            return Collections.emptyList();
        }
        return new LambdaQueryChainWrapper<>(this)
                .eq(OutboundOrderPlatformNo::getTenantId, TenentContext.getTenantId())
                .eq(OutboundOrderPlatformNo::getDeleted, false)
                .in(OutboundOrderPlatformNo::getWoId, woIds)
                .list();
    }

    default void removeByWoIds(Set<String> woIds) {
        if (CollectionUtils.isEmpty(woIds)) {
            return;
        }

        new LambdaUpdateChainWrapper<>(this)
                .eq(OutboundOrderPlatformNo::getTenantId, TenentContext.getTenantId())
                .eq(OutboundOrderPlatformNo::getDeleted, false)
                .in(OutboundOrderPlatformNo::getWoId, woIds)
                .set(OutboundOrderPlatformNo::getDeleteTime, LocalDateTime.now())
                .set(OutboundOrderPlatformNo::getDeleted, true)
                .update();
    }

}
