package com.dhp.oms.ds.infrastructure.mapper;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import com.dhp.oms.framework.context.user.OpsUserContext;
import com.dhp.oms.framework.core.entity.ops.OpsUser;
import com.dhp.oms.framework.core.mapper.AbstractBaseMapper;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.ibatis.annotations.Mapper;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Set;

/**
 * <p>
 * ops端用户表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-07
 */
@Mapper
public interface OpsUserMapper extends AbstractBaseMapper<OpsUser> {

    default OpsUser findByLoginAccount(String loginAccount) {
        return new LambdaQueryChainWrapper<>(this)
                .eq(OpsUser::getLoginAccount, loginAccount)
                .eq(OpsUser::getDeleted, Boolean.FALSE)
                .one();
    }

    default OpsUser findByPhone(String phone) {
        return new LambdaQueryChainWrapper<>(this)
                .eq(OpsUser::getPhone, phone)
                .eq(OpsUser::getDeleted, Boolean.FALSE)
                .one();
    }

    default OpsUser findByMail(String mail) {
        return new LambdaQueryChainWrapper<>(this)
                .eq(OpsUser::getMail, mail)
                .eq(OpsUser::getDeleted, Boolean.FALSE)
                .one();
    }

    default OpsUser findByUserId(Long userId) {
        return new LambdaQueryChainWrapper<>(this)
                .eq(OpsUser::getId, userId)
                .eq(OpsUser::getDeleted, Boolean.FALSE)
                .one();
    }

    default void updatePassword(Long userId, String encryptedPassword) {
        LambdaUpdateWrapper<OpsUser> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(OpsUser::getId, userId)
                .set(OpsUser::getLoginPassword, encryptedPassword)
                .set(OpsUser::getLoginPasswordResetted, Boolean.TRUE)
                .set(OpsUser::getUpdateTime, LocalDateTime.now())
                .set(OpsUser::getUpdateBy, OpsUserContext.getUserId());
        this.update(updateWrapper);
    }

//    default void updateNotifyScheme(Long userId, Set<String> notifySchemes) {
//        if (CollectionUtils.isEmpty(notifySchemes)) {
//            return;
//        }
//        List<UserNotifySchemeJSON> list = notifySchemes.stream()
//                .map(UserNotifySchemeJSON::new)
//                .collect(Collectors.toList());
//        String value = ObjectMapperUtil.obj2Json(list);
//
//        new LambdaUpdateChainWrapper<>(this)
//                .eq(OpsUser::getTenantId, OpsUserContext.getTenantId())
//                .eq(OpsUser::getId, userId)
//                .set(OpsUser::getUserNotifyScheme, value)
//                .set(OpsUser::getUpdateTime, LocalDateTime.now())
//                .set(OpsUser::getUpdateBy, OpsUserContext.getUserId())
//                .update();
//    }

    default void updateFirstLoginCompleted(Long userId) {
        new LambdaUpdateChainWrapper<>(this)
                .eq(OpsUser::getId, userId)
                .set(OpsUser::getFirstLoginCompleted, Boolean.TRUE)
                .set(OpsUser::getUpdateTime, LocalDateTime.now())
                .set(OpsUser::getUpdateBy, OpsUserContext.getUserId())
                .update();
    }

    default List<OpsUser> list() {
        return new LambdaQueryChainWrapper<>(this)
                .eq(OpsUser::getCompanyId, OpsUserContext.getCompanyId())
                .eq(OpsUser::getDeleted, Boolean.FALSE)
                .orderByDesc(OpsUser::getId)
                .list();
    }

    default void removeById(Long id) {
        new LambdaUpdateChainWrapper<>(this)
                .eq(OpsUser::getId, id)
                .set(OpsUser::getDeleted, Boolean.TRUE)
                .set(OpsUser::getDeletedBy, OpsUserContext.getUserId())
                .set(OpsUser::getDeleteTime, LocalDateTime.now())
                .update();
    }

//    default void updateByReqequest(UserUpdateRequest request) {
//        new LambdaUpdateChainWrapper<>(this)
//                .eq(OpsUser::getId, request.getUserId())
//                .eq(OpsUser::getTenantId, OpsUserContext.getTenantId())
//                .set(OpsUser::getCompanyId, request.getCompanyId())
//                .set(OpsUser::getDepartmentId, request.getDepartmentId())
//                .set(OpsUser::getLeaderUserId, request.getLeaderUserId())
//                .set(StringUtils.isNoneBlank(request.getLoginPassword()), OpsUser::getLoginPassword, PasswordUtil.getEncrypted(request.getLoginPassword()))
//                .set(OpsUser::getUsername, request.getUsername())
//                .set(OpsUser::getPhone, request.getPhone())
//                .set(OpsUser::getMail, request.getMail())
//                .set(OpsUser::getStatus, request.getStatus())
//                .set(OpsUser::getRemark, request.getRemark())
//                .set(OpsUser::getLeaderUserId, request.getLeaderUserId())
//                .set(OpsUser::getUpdateBy, OpsUserContext.getUserId())
//                .set(OpsUser::getUpdateTime, LocalDateTime.now())
//                .update();
//    }

    default List<OpsUser> findByUserIds(Set<Long> userIds) {
        if (CollectionUtils.isEmpty(userIds)) {
            return Collections.emptyList();
        }
        return new LambdaQueryChainWrapper<>(this)
                .eq(OpsUser::getTenantId, OpsUserContext.getTenantId())
                .in(OpsUser::getId, userIds)
                .eq(OpsUser::getDeleted, Boolean.FALSE)
                .list();
    }

}
