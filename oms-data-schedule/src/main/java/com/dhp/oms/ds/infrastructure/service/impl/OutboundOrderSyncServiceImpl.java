package com.dhp.oms.ds.infrastructure.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.dhp.oms.ds.infrastructure.generator.OutboundOrderNumberGenerator;
import com.dhp.oms.ds.infrastructure.mapper.outboundorder.OutboundOrderMapper;
import com.dhp.oms.ds.infrastructure.mapper.outboundorder.OutboundOrderPlatformNoMapper;
import com.dhp.oms.ds.infrastructure.mapper.outboundorder.OutboundOrderSkuMapper;
import com.dhp.oms.ds.infrastructure.service.OutboundOrderSyncService;
import com.dhp.oms.framework.context.tenant.TenentContext;
import com.dhp.oms.framework.core.entity.ops.outboundorder.OutboundOrder;
import com.dhp.oms.framework.core.entity.ops.outboundorder.OutboundOrderPlatformNo;
import com.dhp.oms.framework.core.entity.ops.outboundorder.OutboundOrderSku;
import com.dhp.oms.framework.core.enums.ops.outboundorder.OutboundOrderOrderTypeEnum;
import com.dhp.oms.framework.core.enums.ops.outboundorder.OutboundOrderStatusEnum;
import com.dhp.oms.framework.core.enums.ops.outboundorder.OutboundOrderTypeEnum;
import com.dhp.oms.framework.utils.ObjectMapperUtil;
import com.dhp.oms.framework.utils.SnowflakeIdWorkerUtils;
import com.dhp.oms.third.api.common.ExternApiResult;
import com.dhp.oms.third.api.module.outboundorder.OutboundOrderApi;
import com.dhp.oms.third.api.module.outboundorder.request.AmazonFulfilledShipmentRequest;
import com.dhp.oms.third.api.module.outboundorder.request.WmsOrdeRequest;
import com.dhp.oms.third.api.module.outboundorder.response.AmazonFulfilledShipmentResponse;
import com.dhp.oms.third.api.module.outboundorder.response.WmsOrdeResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@RequiredArgsConstructor
@Service
public class OutboundOrderSyncServiceImpl implements OutboundOrderSyncService {

    private final OutboundOrderApi outboundOrderApi;
    private final OutboundOrderMapper outboundOrderMapper;
    private final OutboundOrderSkuMapper outboundOrderSkuMapper;
    private final OutboundOrderPlatformNoMapper outboundOrderPlatformNoMapper;
    private final OutboundOrderNumberGenerator outboundOrderNumberGenerator;

    @Override
    public Integer sync(WmsOrdeRequest request) {
        ExternApiResult<List<WmsOrdeResponse>> result = outboundOrderApi.getPurchasePlans(request);
        if (!result.isSuccess()) {
            return -1;
        }

        if (CollectionUtils.isEmpty(result.getData())) {
            return 0;
        }

        List<WmsOrdeResponse> data = result.getData();
        Map<String, WmsOrdeResponse> wmsOrdeResponseMap = data.stream().collect(Collectors.toMap(WmsOrdeResponse::getWo_id, Function.identity(), (v1, v2) -> v1));
        Set<String> woIds = wmsOrdeResponseMap.keySet();

        Map<String, OutboundOrder> outboundOrderMap = outboundOrderMapper.findSelfShippingByWoIds(woIds)
                .stream().collect(Collectors.toMap(OutboundOrder::getWoId, Function.identity(), (v1, v2) -> v1));

        // 对主表、产品明细表进行插入或者更新
        data.stream().collect(Collectors.groupingBy(x -> outboundOrderMap.get(x.getWo_id()) != null))
                .forEach((k, v) -> {
                    if (Boolean.TRUE.equals(k)) {
                        doUpdate(v, outboundOrderMap);
                    } else {
                        doInsert(v);
                    }
                });

        List<OutboundOrderPlatformNo> outboundOrderPlatformNoList = new ArrayList<>();
        data.forEach(x -> {
            List<OutboundOrderPlatformNo> platformNoList = Optional.ofNullable(x.getPlatform_order_no()).orElse(Collections.emptySet()).stream()
                    .map(v -> {
                        OutboundOrderPlatformNo platformNo = new OutboundOrderPlatformNo();
                        platformNo.setId(SnowflakeIdWorkerUtils.generateId());
                        platformNo.setTenantId(TenentContext.getTenantId());
                        platformNo.setPlatformOrderNo(v);
                        platformNo.setWoId(x.getWo_id());
                        return platformNo;
                    })
                    .toList();
            outboundOrderPlatformNoList.addAll(platformNoList);
        });

        if (CollectionUtils.isNotEmpty(outboundOrderPlatformNoList)) {
            Map<String, OutboundOrderPlatformNo> outboundOrderPlatformNoMap = outboundOrderPlatformNoMapper.findByWoIds(woIds)
                    .stream().collect(Collectors.toMap(OutboundOrderPlatformNo::getPlatformOrderNo, Function.identity(), (v1, v2) -> v1));

            outboundOrderPlatformNoList.stream().collect(Collectors.groupingBy(x -> outboundOrderPlatformNoMap.get(x.getPlatformOrderNo()) != null))
                    .forEach((k, v) -> {
                        // tips: 平台单号不至于会发生改变吧？
                        if (Boolean.FALSE.equals(k)) {
                            outboundOrderPlatformNoMapper.saveBatch(v);
                        }
                    });
        }


        log.info("销售出库单同步成功，数量：{}", result.getData().size());

        return result.getData().size();
    }

    private void doUpdate(List<WmsOrdeResponse> responses, Map<String, OutboundOrder> outboundOrderMap) {
        for (WmsOrdeResponse response : responses) {
            OutboundOrder dbOrder = outboundOrderMap.get(response.getWo_id());
            if (dbOrder == null) {
                continue;
            }
            OutboundOrder outboundOrder = convertOutboundOrder(response);
            outboundOrder.setUpdateTime(LocalDateTime.now());

            LambdaQueryWrapper<OutboundOrder> wrapper = Wrappers.<OutboundOrder>lambdaQuery()
                    .eq(OutboundOrder::getId, dbOrder.getId())
                    .eq(OutboundOrder::getTenantId, dbOrder.getTenantId());
            outboundOrderMapper.update(outboundOrder, wrapper);

            Map<String, OutboundOrderSku> outboundOrderSkuMap = outboundOrderSkuMapper.findByWoId(response.getWo_id())
                    .stream().collect(Collectors.toMap(OutboundOrderSku::getWodId, Function.identity(), (v1, v2) -> v1));


            Optional.ofNullable(response.getProduct_info()).orElse(Collections.emptyList())
                    .forEach(x -> {
                        OutboundOrderSku outboundOrderSku = convertOutboundOrderSku(x);
                        OutboundOrderSku orderSku = outboundOrderSkuMap.get(x.getWod_id());
                        if (orderSku != null) {
                            outboundOrderSku.setId(orderSku.getId());
                            outboundOrderSku.setUpdateTime(LocalDateTime.now());

                            LambdaQueryWrapper<OutboundOrderSku> skuWrapper = Wrappers.<OutboundOrderSku>lambdaQuery()
                                    .eq(OutboundOrderSku::getId, dbOrder.getId())
                                    .eq(OutboundOrderSku::getTenantId, dbOrder.getTenantId());
                            outboundOrderSkuMapper.update(outboundOrderSku, skuWrapper);
                        } else {
                            outboundOrderSku.setId(SnowflakeIdWorkerUtils.generateId());
                            outboundOrderSku.setTenantId(TenentContext.getTenantId());
                            outboundOrderSku.setOutboundOrderId(dbOrder.getId());
                            outboundOrderSku.setWoId(outboundOrder.getWoId());

                            outboundOrderSkuMapper.insert(outboundOrderSku);
                        }
                    });

        }
    }

    private void doInsert(List<WmsOrdeResponse> responses) {
        List<String> outboundOrderNumbers = outboundOrderNumberGenerator.generate(TenentContext.getTenantId(), responses.size());

        List<OutboundOrder> outboundOrderList = new ArrayList<>();
        List<OutboundOrderSku> outboundOrderSkuList = new ArrayList<>();
        for (int i = 0; i < responses.size(); i++) {
            WmsOrdeResponse response = responses.get(i);

            OutboundOrder outboundOrder = convertOutboundOrder(response);
            outboundOrder.setId(SnowflakeIdWorkerUtils.generateId());
            outboundOrder.setTenantId(TenentContext.getTenantId());
            outboundOrder.setOutboundOrderNumber(outboundOrderNumbers.get(i));
            outboundOrderList.add(outboundOrder);

            List<OutboundOrderSku> skuList = Optional.ofNullable(response.getProduct_info()).orElse(Collections.emptyList()).stream()
                    .map(x -> {
                        OutboundOrderSku outboundOrderSku = convertOutboundOrderSku(x);
                        outboundOrderSku.setId(SnowflakeIdWorkerUtils.generateId());
                        outboundOrderSku.setTenantId(TenentContext.getTenantId());
                        outboundOrderSku.setWoId(response.getWo_id());
                        outboundOrderSku.setOutboundOrderId(outboundOrder.getId());
                        return outboundOrderSku;
                    })
                    .toList();
            outboundOrderSkuList.addAll(skuList);


        }

        if (CollectionUtils.isNotEmpty(outboundOrderList)) {
            outboundOrderMapper.saveBatch(outboundOrderList);
        }
        if (CollectionUtils.isNotEmpty(outboundOrderSkuList)) {
            outboundOrderSkuMapper.saveBatch(outboundOrderSkuList);
        }
    }


    private OutboundOrder convertOutboundOrder(WmsOrdeResponse response) {
        OutboundOrder x = new OutboundOrder();

        x.setType(OutboundOrderTypeEnum.SELF_SHIPPING.name());
        x.setWoId(response.getWo_id());

        Integer status = Optional.ofNullable(OutboundOrderStatusEnum.findByCode(response.getStatus())).map(OutboundOrderStatusEnum::getCode).orElse(null);
        x.setStatus(status);
        x.setSid(response.getSid());
        x.setWaybillNo(response.getWaybill_no());
        x.setTrackingNo(response.getTracking_no());
        x.setOrderNumber(response.getOrder_number());
        x.setWoNumber(response.getWo_number());
        x.setOrderType(response.getOrder_type());
        x.setWid(response.getWid());
        x.setLogisticsStatus(response.getLogistics_status());
        x.setLogisticsFreight(response.getLogistics_freight());
        x.setLogisticsFreightCurrencyCode(response.getLogistics_freight_currency_code());
        x.setLogisticsEstimatedFreight(response.getLogistics_estimated_freight());
        x.setLogisticsEstimatedFreightCurrencyCode(response.getLogistics_estimated_freight_currency_code());

        x.setSurfaceFileType(response.getSurface_file_type());
        x.setSurfaceFile(ObjectMapperUtil.obj2Json(response.getSurface_file()));

        x.setPkgVolume(response.getPkg_volume());
        x.setPkgLength(response.getPkg_length());
        x.setPkgWidth(response.getPkg_width());
        x.setPkgHeight(response.getPkg_height());
        x.setPkgSizeUnit(response.getPkg_size_unit());
        x.setPkgWeight(response.getPkg_weight());
        x.setPkgRealWeight(response.getPkg_real_weight());
        x.setPkgFeeWeight(response.getPkg_fee_weight());
        x.setPkgWeightUnit(response.getPkg_weight_unit());
        x.setPkgRealWeightUnit(response.getPkg_real_weight_unit());
        x.setPkgFeeWeightUnit(response.getPkg_fee_weight_unit());

        x.setRecipientTaxNo(response.getRecipient_tax_no());
        x.setSenderTaxNo(response.getSender_tax_no());

        x.setConsignee(response.getConsignee());
        x.setConsigneePhone(response.getConsignee_phone());
        x.setConsigneePostcode(response.getConsignee_postcode());
        x.setConsigneeAddress(response.getConsignee_address());
        x.setConsigneeFullAddress(response.getConsignee_full_address());

        x.setOrderCustomerServiceNotes(response.getOrder_customer_service_notes());
        x.setOrderBuyerNotes(response.getOrder_buyer_notes());

        x.setDeliverDeadline(response.getDeliver_deadline());
        x.setCreateAt(response.getCreate_at());
        x.setUpdateAt(response.getUpdate_at());
        x.setDeliveredAt(response.getDelivered_at());

        return x;
    }

    private OutboundOrderSku convertOutboundOrderSku(WmsOrdeResponse.ProductInfo productInfo) {
        OutboundOrderSku sku = new OutboundOrderSku();
        sku.setWodId(productInfo.getWod_id());
        sku.setProductId(productInfo.getProduct_id());
        sku.setSku(productInfo.getSku());
        sku.setCount(productInfo.getCount());
        sku.setProductName(productInfo.getProduct_name());
        sku.setSellerSku(productInfo.getSeller_sku());
        sku.setCustomization(productInfo.getCustomization());
        sku.setCnName(productInfo.getCn_name());
        sku.setEnName(productInfo.getEn_name());
        sku.setThirdProductCode(productInfo.getThird_product_code());
        sku.setThirdProductName(productInfo.getThird_product_name());

        return sku;
    }

    @Override
    public Integer syncAmazonFBA(AmazonFulfilledShipmentRequest request) {
        ExternApiResult<List<AmazonFulfilledShipmentResponse>> result = outboundOrderApi.getAmazonFulfilledShipmentsList(request);
        if (!result.isSuccess()) {
            return -1;
        }
        List<AmazonFulfilledShipmentResponse> amazonFulfilledShipmentResponses = result.getData();
        if (CollectionUtils.isEmpty(amazonFulfilledShipmentResponses)) {
            return 0;
        }

        Set<String> woIds = amazonFulfilledShipmentResponses.stream()
                .map(AmazonFulfilledShipmentResponse::getShipment_item_id).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(woIds)) {
            return 0;
        }

        Map<String, OutboundOrder> orderMap = outboundOrderMapper.findAmazonFbaByWoIds(woIds).stream()
                .collect(Collectors.toMap(OutboundOrder::getWoId, Function.identity(), (v1, v2) -> v1));
        Map<String, List<OutboundOrderSku>> skuMap = outboundOrderSkuMapper.findByWoIds(woIds).stream()
                .collect(Collectors.groupingBy(OutboundOrderSku::getWoId));
        Map<String, List<OutboundOrderPlatformNo>> platNoMap = outboundOrderPlatformNoMapper.findByWoIds(woIds).stream()
                .collect(Collectors.groupingBy(OutboundOrderPlatformNo::getWoId));

        List<OutboundOrder> outboundOrderList = new ArrayList<>();
        List<OutboundOrderSku> outboundOrderSkuList = new ArrayList<>();
        List<OutboundOrderPlatformNo> outboundOrderPlatformNoList = new ArrayList<>();
        amazonFulfilledShipmentResponses.forEach(x -> {
            OutboundOrder outboundOrder = orderMap.get(x.getWoId());
            OutboundOrder convertOutboundOrder = convertOutboundOrder(x);
            if (outboundOrder == null) {
                convertOutboundOrder.setId(SnowflakeIdWorkerUtils.generateId());
                convertOutboundOrder.setTenantId(TenentContext.getTenantId());
                convertOutboundOrder.setId(SnowflakeIdWorkerUtils.generateId());
                convertOutboundOrder.setType(OutboundOrderTypeEnum.AMAZON_FBA.name());
                // prd标注默认已出库
                convertOutboundOrder.setStatus(OutboundOrderStatusEnum.OUT_STOCKED.getCode());
                // 默认一单一件
                convertOutboundOrder.setOrderType(OutboundOrderOrderTypeEnum.ONE_P_ONE_T.getCode());

                outboundOrderList.add(convertOutboundOrder);

                outboundOrder = convertOutboundOrder;
            } else {
                LambdaQueryWrapper<OutboundOrder> wrapper = Wrappers.<OutboundOrder>lambdaQuery()
                        .eq(OutboundOrder::getId, outboundOrder.getId())
                        .eq(OutboundOrder::getTenantId, TenentContext.getTenantId());

                convertOutboundOrder.setUpdateTime(LocalDateTime.now());

                outboundOrderMapper.update(convertOutboundOrder, wrapper);
            }

            Map<String, OutboundOrderSku> orderSkuMap = skuMap.getOrDefault(x.getWoId(), Collections.emptyList()).stream()
                    .collect(Collectors.toMap(OutboundOrderSku::getWodId, Function.identity(), (v1, v2) -> v1));
            OutboundOrderSku convertOutboundOrderSku = convertOutboundOrderSku(x);
            OutboundOrderSku outboundOrderSku = orderSkuMap.get(convertOutboundOrderSku.getWodId());
            if (outboundOrderSku == null) {
                convertOutboundOrderSku.setId(SnowflakeIdWorkerUtils.generateId());
                convertOutboundOrderSku.setTenantId(TenentContext.getTenantId());
                convertOutboundOrderSku.setOutboundOrderId(outboundOrder.getId());
                convertOutboundOrderSku.setWoId(x.getWoId());

                outboundOrderSkuList.add(convertOutboundOrderSku);
            } else {
                LambdaQueryWrapper<OutboundOrderSku> wrapper = Wrappers.<OutboundOrderSku>lambdaQuery()
                        .eq(OutboundOrderSku::getId, outboundOrderSku.getId())
                        .eq(OutboundOrderSku::getTenantId, TenentContext.getTenantId());

                convertOutboundOrderSku.setUpdateTime(LocalDateTime.now());

                outboundOrderSkuMapper.update(convertOutboundOrderSku, wrapper);
            }

            List<OutboundOrderPlatformNo> platformNoList = platNoMap.get(x.getWoId());
            OutboundOrderPlatformNo platformNo = convertOutboundOrderPlatformNo(x);
            if (CollectionUtils.isEmpty(platformNoList)) {
                platformNo.setId(SnowflakeIdWorkerUtils.generateId());
                platformNo.setTenantId(TenentContext.getTenantId());

                outboundOrderPlatformNoList.add(platformNo);
            }

        });

        if (CollectionUtils.isNotEmpty(outboundOrderList)) {
            List<String> list = outboundOrderNumberGenerator.generate(TenentContext.getTenantId(), outboundOrderList.size());
            for (int i = 0; i < outboundOrderList.size(); i++) {
                OutboundOrder outboundOrder = outboundOrderList.get(i);
                outboundOrder.setOutboundOrderNumber(list.get(i));
            }

            outboundOrderMapper.saveBatch(outboundOrderList);
        }
        if (CollectionUtils.isNotEmpty(outboundOrderSkuList)) {
            outboundOrderSkuMapper.saveBatch(outboundOrderSkuList);
        }
        if (CollectionUtils.isNotEmpty(outboundOrderPlatformNoList)) {
            outboundOrderPlatformNoMapper.saveBatch(outboundOrderPlatformNoList);
        }

        return amazonFulfilledShipmentResponses.size();
    }

    private OutboundOrder convertOutboundOrder(AmazonFulfilledShipmentResponse response) {
        OutboundOrder x = new OutboundOrder();
        x.setWoId(response.getShipment_item_id());
        x.setSid(response.getSid());
        x.setWoNumber(response.getShipment_id());
        x.setDeliverDeadline(response.getEstimated_arrival_date_locale());
        x.setTrackingNo(response.getTracking_number());
        x.setDeliveredAt(response.getShipment_date_locale());

        return x;
    }

    private OutboundOrderSku convertOutboundOrderSku(AmazonFulfilledShipmentResponse response) {
        OutboundOrderSku x = new OutboundOrderSku();
        x.setWoId(response.getShipment_item_id());
        x.setWodId(response.getAmazon_order_item_id());
        x.setSku(response.getLocal_sku());
        x.setCount(response.getQuantity_shipped());
        x.setProductName(response.getProduct_name());
        x.setSellerSku(response.getMsku());
        return x;
    }

    private OutboundOrderPlatformNo convertOutboundOrderPlatformNo(AmazonFulfilledShipmentResponse response) {
        OutboundOrderPlatformNo x = new OutboundOrderPlatformNo();
        x.setWoId(response.getShipment_item_id());
        x.setPlatformOrderNo(response.getAmazon_order_id());
        return x;
    }

}
