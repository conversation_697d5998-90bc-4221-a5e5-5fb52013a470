package com.dhp.oms.ds.infrastructure.service.impl;

import com.dhp.oms.ds.infrastructure.service.AbstractSaleOrderSyncService;
import com.dhp.oms.framework.context.user.OpsUserContext;
import com.dhp.oms.framework.core.entity.ops.order.OpsOrderSaleOrder;
import com.dhp.oms.framework.core.entity.ops.order.OpsOrderSaleOrderSku;
import com.dhp.oms.framework.core.entity.ops.store.StoreInfo;
import com.dhp.oms.framework.utils.CommonUtils;
import com.dhp.oms.framework.utils.SnowflakeIdWorkerUtils;
import com.dhp.oms.third.api.common.ExternApiResult;
import com.dhp.oms.third.api.module.order.FbmOrderApi;
import com.dhp.oms.third.api.module.order.request.FbmOrderDetailRequest;
import com.dhp.oms.third.api.module.order.request.FbmOrderRequest;
import com.dhp.oms.third.api.module.order.response.FbmOrderDetailResponse;
import com.dhp.oms.third.api.module.order.response.FbmOrderResponse;
import com.google.common.collect.Lists;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.BiConsumer;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service("fbmOrderSyncServiceImpl")
public class FbmOrderSyncServiceImpl extends AbstractSaleOrderSyncService<FbmOrderRequest, FbmOrderResponse, FbmOrderDetailResponse, FbmOrderDetailResponse.OrderItem> {

    @Resource
    private FbmOrderApi fbmOrderApi;

    @Override
    protected FbmOrderRequest newOrderListRequestInstance(List<String> partitionSidList) {
        FbmOrderRequest fbmOrderRequest = new FbmOrderRequest();
        fbmOrderRequest.setSid(String.join(",", partitionSidList));
        return fbmOrderRequest;
    }

    @Override
    protected boolean firstPage(FbmOrderRequest fbmOrderRequest) {
        return fbmOrderRequest.getPage() == 1;
    }

    @Override
    protected boolean hasNextPage(FbmOrderRequest fbmOrderRequest, Long total) {
        return (long) fbmOrderRequest.getPage() * fbmOrderRequest.getLength() < total;
    }

    @Override
    protected void setNextPage(FbmOrderRequest fbmOrderRequest) {
        fbmOrderRequest.setPage(fbmOrderRequest.getPage() + 1);
    }

    @Override
    protected List<FbmOrderResponse> getOrderList(ExternApiResult<?> result) {
        return Optional.ofNullable(result)
                .map(ExternApiResult::getData)
                .map(item -> (List<FbmOrderResponse>) item)
                .orElse(Collections.emptyList());
    }

    @Override
    protected Function<FbmOrderResponse, String> obtainGetOrderIdFunc() {
        return FbmOrderResponse::getOrder_number;
    }

    @Override
    protected BiConsumer<FbmOrderResponse, Long> obtainSetBizOrderIdFunc() {
        return FbmOrderResponse::setBizOrderId;
    }

    @Override
    protected Function<FbmOrderResponse, Long> obtainGetBizOrderIdFunc() {
        return FbmOrderResponse::getBizOrderId;
    }

    @Override
    protected Function<FbmOrderDetailResponse.OrderItem, String> obtainGetOrderItemIdFunc() {
        return FbmOrderDetailResponse.OrderItem::getOrder_item_no;
    }

    @Override
    protected BiConsumer<FbmOrderDetailResponse.OrderItem, Long> obtainSetBizOrderItemIdFunc() {
        return FbmOrderDetailResponse.OrderItem::setBizOrderItemId;
    }

    @Override
    protected Function<FbmOrderDetailResponse.OrderItem, Long> obtainGetBizOrderItemIdFunc() {
        return FbmOrderDetailResponse.OrderItem::getBizOrderItemId;
    }

    @Override
    protected List<FbmOrderDetailResponse.OrderItem> obtainOrderSkuList(FbmOrderResponse fbmOrderResponse, FbmOrderDetailResponse fbmOrderDetailResponse) {
        return fbmOrderDetailResponse.getOrder_item();
    }

    @Override
    protected ExternApiResult<?> getOrderResponse(FbmOrderRequest fbmOrderRequest, LocalDateTime beginTime, LocalDateTime endTime) {
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
//        fbmOrderRequest.setOrder_status("Pending");
        fbmOrderRequest.setStart_time(dateTimeFormatter.format(beginTime));
        fbmOrderRequest.setEnd_time(dateTimeFormatter.format(endTime));
        return fbmOrderApi.orders(fbmOrderRequest);
    }

    @Override
    protected Map<String, FbmOrderDetailResponse> getOrderDetailResponse(List<FbmOrderResponse> orderList) {
        return orderList.stream()
                .map(response -> {
                    FbmOrderDetailRequest fbmOrderDetailRequest = new FbmOrderDetailRequest();
                    fbmOrderDetailRequest.setOrder_number(response.getOrder_number());
                    return fbmOrderApi.orderDetail(fbmOrderDetailRequest);
                })
                .map(ExternApiResult::getData)
                .collect(Collectors.toMap(
                        FbmOrderDetailResponse::getOrder_number,
                        Function.identity(),
                        (pre, next) -> next));
    }

    @Override
    protected void buildInsertOrder(FbmOrderRequest orderListRequest, FbmOrderResponse orderResponse, FbmOrderDetailResponse orderDetailResponse, OpsOrderSaleOrder opsOrderSaleOrder) {
        // todo fbm平台订单orderNum是列表
        opsOrderSaleOrder.setId(SnowflakeIdWorkerUtils.generateId());
        opsOrderSaleOrder.setTenantId(OpsUserContext.getTenantId());
        opsOrderSaleOrder.setSid(orderListRequest.getSid()); // 每批只有一个sid
        opsOrderSaleOrder.setOrigin("FBM");
        opsOrderSaleOrder.setSalePlatform(orderDetailResponse.getPlatform());
        opsOrderSaleOrder.setPlatformOrderNo(orderDetailResponse.getOrder_number());
        opsOrderSaleOrder.setReferenceNo(null);
        opsOrderSaleOrder.setSalesChannel(null);
        opsOrderSaleOrder.setPurchaseOrderNumber(null);
        opsOrderSaleOrder.setOmsOrderNo(null);
        opsOrderSaleOrder.setCreateBy(OpsUserContext.getTenantId());
        opsOrderSaleOrder.setCreateTime(LocalDateTime.now());
        buildUpdateOrder(orderResponse, orderDetailResponse, opsOrderSaleOrder);
    }

    @Override
    protected void buildUpdateOrder(FbmOrderResponse orderResponse, FbmOrderDetailResponse orderDetailResponse, OpsOrderSaleOrder opsOrderSaleOrder) {
        opsOrderSaleOrder.setFulfillmentChannel("MFN");
        opsOrderSaleOrder.setOrderStatus(orderDetailResponse.getOrder_status());
        opsOrderSaleOrder.setCurrency(null);
        opsOrderSaleOrder.setIsAssessed(null);
        opsOrderSaleOrder.setIsMcfOrder(null);
        opsOrderSaleOrder.setIsReturnOrder(null);
        opsOrderSaleOrder.setIsReplacedOrder(null);
        opsOrderSaleOrder.setIsReplacementOrder(null);
        opsOrderSaleOrder.setPurchaseDateLocal(CommonUtils.convertStringToLocalDateTime(orderDetailResponse.getPurchase_time(), CommonUtils.DATETIME_FORMATTER, ZoneId.of("UTC"), null)); // 没有返回时区和站点，当作utc
        opsOrderSaleOrder.setPurchaseDateLocalUtc(CommonUtils.convertStringToLocalDateTime(orderDetailResponse.getPurchase_time(), CommonUtils.DATETIME_FORMATTER, ZoneId.of("UTC"), null));
        opsOrderSaleOrder.setPurchaseDateLocalBjt(CommonUtils.convertStringToLocalDateTime(orderDetailResponse.getPurchase_time(), CommonUtils.DATETIME_FORMATTER, ZoneId.of("UTC"), ZoneId.of("Asia/Shanghai")));
        opsOrderSaleOrder.setLastUpdateDate(null);
        opsOrderSaleOrder.setLastUpdateDateUtc(null);
        opsOrderSaleOrder.setPaymentMethod(null);
        opsOrderSaleOrder.setPostedDate(null);
        opsOrderSaleOrder.setPostedDateUtc(null);
        opsOrderSaleOrder.setPostedDateBjt(null);
        opsOrderSaleOrder.setPostedStatus(null);
        opsOrderSaleOrder.setShipServiceLevel(null);
        opsOrderSaleOrder.setOrderType(null);
        opsOrderSaleOrder.setLatestShipDate(null);
        opsOrderSaleOrder.setRemark(orderDetailResponse.getCustomer_comment());
        opsOrderSaleOrder.setTrackingNo(orderDetailResponse.getTracking_number());
        opsOrderSaleOrder.setSenderTaxNo(null);

        opsOrderSaleOrder.setName(null);
        opsOrderSaleOrder.setPhone(null);
        opsOrderSaleOrder.setEmail(null);
        opsOrderSaleOrder.setBuyerRemark(null);
        opsOrderSaleOrder.setRecipient(null);
        opsOrderSaleOrder.setAddressLine1(null);
        opsOrderSaleOrder.setAddressLine2(null);
        opsOrderSaleOrder.setAddressLine3(null);
        opsOrderSaleOrder.setCountry(null);
        opsOrderSaleOrder.setCity(null);
        opsOrderSaleOrder.setStateProvince(null);
        opsOrderSaleOrder.setDistrictCounty(null);
        opsOrderSaleOrder.setPostalCode(null);

        opsOrderSaleOrder.setIsBusinessOrder(null);
        opsOrderSaleOrder.setIsPrime(null);
        opsOrderSaleOrder.setIsPremiumOrder(null);
        opsOrderSaleOrder.setIsPromotion(null);
        opsOrderSaleOrder.setOrderTotalAmount(null);
        opsOrderSaleOrder.setShippingPriceAmount(null);
        opsOrderSaleOrder.setTaxAmount(null);
        opsOrderSaleOrder.setPredictProfit(null);
        opsOrderSaleOrder.setPredictGrossProfit(null);
        opsOrderSaleOrder.setUpdateBy(OpsUserContext.getTenantId());
        opsOrderSaleOrder.setUpdateTime(LocalDateTime.now());
    }

    @Override
    protected void buildInsertOrderSku(FbmOrderResponse orderResponse, FbmOrderDetailResponse orderDetailResponse, FbmOrderDetailResponse.OrderItem orderItem, OpsOrderSaleOrder opsOrderSaleOrder, OpsOrderSaleOrderSku opsOrderSaleOrderSku) {
        opsOrderSaleOrderSku.setId(SnowflakeIdWorkerUtils.generateId());
        opsOrderSaleOrderSku.setTenantId(OpsUserContext.getTenantId());
        opsOrderSaleOrderSku.setAsin(null);
        opsOrderSaleOrderSku.setMsku(orderItem.getMSKU());
        opsOrderSaleOrderSku.setSku(orderItem.getSku());
        opsOrderSaleOrderSku.setPlatform_order_no(orderItem.getPlatform_order_id());
        opsOrderSaleOrderSku.setCreateBy(OpsUserContext.getTenantId());
        opsOrderSaleOrderSku.setCreateTime(LocalDateTime.now());
        buildUpdateOrderSku(orderResponse, orderDetailResponse, orderItem, opsOrderSaleOrder, opsOrderSaleOrderSku);
    }

    @Override
    protected void buildUpdateOrderSku(FbmOrderResponse fbmOrderResponse, FbmOrderDetailResponse fbmOrderDetailResponse, FbmOrderDetailResponse.OrderItem orderItem, OpsOrderSaleOrder opsOrderSaleOrder, OpsOrderSaleOrderSku opsOrderSaleOrderSku) {
        opsOrderSaleOrderSku.setTitle(null);
        opsOrderSaleOrderSku.setProductName(orderItem.getProduct_name());
        opsOrderSaleOrderSku.setSpecificationInfo(null);
        opsOrderSaleOrderSku.setIsCustomized(null);
        opsOrderSaleOrderSku.setOrderItemId(orderItem.getOrder_item_no());
        opsOrderSaleOrderSku.setQuantityOrdered(orderItem.getQuality());
        opsOrderSaleOrderSku.setQuantityShipped(null);
        opsOrderSaleOrderSku.setUnitPriceAmount(orderItem.getItem_unit_price());
        opsOrderSaleOrderSku.setTaxAmount(null);
        opsOrderSaleOrderSku.setItemPriceAmount(null);
        opsOrderSaleOrderSku.setItemTaxAmount(null);
        opsOrderSaleOrderSku.setRemark(orderItem.getCustomization());
        opsOrderSaleOrderSku.setShippingPriceAmount(null);
        opsOrderSaleOrderSku.setShippingTaxAmount(null);
        opsOrderSaleOrderSku.setShippingDiscountAmount(null);
        opsOrderSaleOrderSku.setShippingDiscountTaxAmount(null);
        opsOrderSaleOrderSku.setGiftWrapPriceAmount(null);
        opsOrderSaleOrderSku.setGiftWrapTaxAmount(null);
        opsOrderSaleOrderSku.setPromotionAmount(null);
        opsOrderSaleOrderSku.setPromotionDiscountTaxAmount(null);
        opsOrderSaleOrderSku.setCodFeeAmount(null);
        opsOrderSaleOrderSku.setCodFeeDiscountAmount(null);
        opsOrderSaleOrderSku.setFbaShipmentAmount(null);
        opsOrderSaleOrderSku.setCommissionAmount(null);
        opsOrderSaleOrderSku.setPointsMonetaryValueAmount(null);
        opsOrderSaleOrderSku.setOtherAmount(null);
        opsOrderSaleOrderSku.setGrossIncome(null);
        opsOrderSaleOrderSku.setInventoryCost(null);
        opsOrderSaleOrderSku.setCgPrice(null);
        opsOrderSaleOrderSku.setCgTransportCosts(null);
        opsOrderSaleOrderSku.setOtherCosts(null);
        opsOrderSaleOrderSku.setProfit(null);
        opsOrderSaleOrderSku.setProfitRate(null);
        opsOrderSaleOrderSku.setUpdateBy(OpsUserContext.getTenantId());
        opsOrderSaleOrderSku.setUpdateTime(LocalDateTime.now());
    }

}
